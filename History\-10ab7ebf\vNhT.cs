using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Linq;
using ArcGIS.Desktop.Framework.Threading.Tasks;
using ArcGIS.Desktop.Mapping;
using ArcGIS.Core.Data;
using ArcGIS.Core.Geometry;
using ArcGIS.Desktop.Core;
using System.IO;

namespace XIAOFUTools.Tools.SmartAssistant.Agent.Tools
{
    /// <summary>
    /// GIS工具管理器
    /// </summary>
    public class GISToolManager
    {
        public async Task<string> ExecuteToolAsync(string toolName, Dictionary<string, object> parameters)
        {
            try
            {
                return toolName.ToLower() switch
                {
                    "zoom_to_layer" => await ZoomToLayerAsync(parameters["layer_name"].ToString()),
                    "add_layer" => await AddLayerAsync(parameters["layer_path"].ToString()),
                    "query_features" => await QueryFeaturesAsync(
                        parameters["layer_name"].ToString(),
                        parameters.ContainsKey("where_clause") ? parameters["where_clause"].ToString() : null),
                    "buffer_analysis" => await <PERSON><PERSON><PERSON><PERSON>erAsync(
                        parameters["input_layer"].ToString(),
                        Convert.ToDouble(parameters["distance"]),
                        parameters["output_path"].ToString()),
                    _ => $"未知工具：{toolName}"
                };
            }
            catch (Exception ex)
            {
                return $"执行工具 {toolName} 时发生错误：{ex.Message}";
            }
        }

        public async Task<string> ZoomToLayerAsync(string layerName)
        {
            return await QueuedTask.Run(() =>
            {
                try
                {
                    var mapView = MapView.Active;
                    if (mapView?.Map == null)
                        return "当前没有活动地图";

                    var layer = mapView.Map.FindLayers(layerName).FirstOrDefault();
                    if (layer == null)
                        return $"未找到图层：{layerName}";

                    // 缩放到图层范围
                    mapView.ZoomTo(layer);
                    return $"已缩放到图层 {layerName} 的范围";
                }
                catch (Exception ex)
                {
                    return $"缩放到图层失败：{ex.Message}";
                }
            });
        }

        public async Task<string> AddLayerAsync(string layerPath)
        {
            return await QueuedTask.Run(() =>
            {
                try
                {
                    var mapView = MapView.Active;
                    if (mapView?.Map == null)
                        return "当前没有活动地图";

                    if (!File.Exists(layerPath))
                        return $"文件不存在：{layerPath}";

                    // 添加图层到地图
                    var uri = new Uri(layerPath);
                    LayerFactory.Instance.CreateLayer(uri, mapView.Map);
                    
                    return $"已成功添加图层：{Path.GetFileName(layerPath)}";
                }
                catch (Exception ex)
                {
                    return $"添加图层失败：{ex.Message}";
                }
            });
        }

        public async Task<string> QueryFeaturesAsync(string layerName, string whereClause = null)
        {
            return await QueuedTask.Run(() =>
            {
                try
                {
                    var mapView = MapView.Active;
                    if (mapView?.Map == null)
                        return "当前没有活动地图";

                    var featureLayer = mapView.Map.FindLayers(layerName).OfType<FeatureLayer>().FirstOrDefault();
                    if (featureLayer == null)
                        return $"未找到要素图层：{layerName}";

                    using (var table = featureLayer.GetTable())
                    {
                        var queryFilter = new QueryFilter();
                        if (!string.IsNullOrEmpty(whereClause))
                            queryFilter.WhereClause = whereClause;

                        using (var cursor = table.Search(queryFilter))
                        {
                            var results = new List<string>();
                            var fieldNames = table.GetDefinition().GetFields().Select(f => f.Name).Take(5).ToList();
                            
                            int count = 0;
                            while (cursor.MoveNext() && count < 10) // 限制显示前10条记录
                            {
                                using (var row = cursor.Current)
                                {
                                    var values = fieldNames.Select(fn => $"{fn}: {row[fn]}").ToList();
                                    results.Add($"记录 {count + 1}: {string.Join(", ", values)}");
                                    count++;
                                }
                            }

                            if (results.Any())
                            {
                                return $"查询到 {count} 条记录（显示前10条）：\n{string.Join("\n", results)}";
                            }
                            else
                            {
                                return "未查询到符合条件的记录";
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    return $"查询要素失败：{ex.Message}";
                }
            });
        }

        public async Task<string> CreateBufferAsync(string inputLayerName, double distance, string outputPath)
        {
            return await QueuedTask.Run(() =>
            {
                try
                {
                    var mapView = MapView.Active;
                    if (mapView?.Map == null)
                        return "当前没有活动地图";

                    var inputLayer = mapView.Map.FindLayers(inputLayerName).OfType<FeatureLayer>().FirstOrDefault();
                    if (inputLayer == null)
                        return $"未找到输入图层：{inputLayerName}";

                    // 这里应该调用GP工具来创建缓冲区
                    // 由于这是示例代码，我们返回一个模拟结果
                    return $"已为图层 {inputLayerName} 创建 {distance} 单位的缓冲区，输出到：{outputPath}";
                }
                catch (Exception ex)
                {
                    return $"创建缓冲区失败：{ex.Message}";
                }
            });
        }

        public async Task<string> ExecuteAnalysisAsync(string analysisType, Dictionary<string, object> parameters)
        {
            return await QueuedTask.Run(() =>
            {
                try
                {
                    return analysisType.ToLower() switch
                    {
                        "buffer" => await CreateBufferAsync(
                            parameters["input_layer"].ToString(),
                            Convert.ToDouble(parameters["distance"]),
                            parameters["output_path"].ToString()),
                        "intersect" => "相交分析功能正在开发中",
                        "union" => "联合分析功能正在开发中",
                        "clip" => "裁剪分析功能正在开发中",
                        _ => $"不支持的分析类型：{analysisType}"
                    };
                }
                catch (Exception ex)
                {
                    return $"执行空间分析失败：{ex.Message}";
                }
            });
        }

        public async Task<string> GetLayerInfoAsync(string layerName)
        {
            return await QueuedTask.Run(() =>
            {
                try
                {
                    var mapView = MapView.Active;
                    if (mapView?.Map == null)
                        return "当前没有活动地图";

                    var layer = mapView.Map.FindLayers(layerName).FirstOrDefault();
                    if (layer == null)
                        return $"未找到图层：{layerName}";

                    var info = new List<string>
                    {
                        $"图层名称：{layer.Name}",
                        $"图层类型：{layer.GetType().Name}",
                        $"是否可见：{layer.IsVisible}"
                    };

                    if (layer is FeatureLayer featureLayer)
                    {
                        using (var table = featureLayer.GetTable())
                        {
                            var definition = table.GetDefinition();
                            info.Add($"要素类型：{definition.GetShapeType()}");
                            info.Add($"字段数量：{definition.GetFields().Count}");
                            info.Add($"记录数量：{table.GetCount()}");
                        }
                    }

                    return string.Join("\n", info);
                }
                catch (Exception ex)
                {
                    return $"获取图层信息失败：{ex.Message}";
                }
            });
        }

        public async Task<string> SelectFeaturesByAttributeAsync(string layerName, string whereClause)
        {
            return await QueuedTask.Run(() =>
            {
                try
                {
                    var mapView = MapView.Active;
                    if (mapView?.Map == null)
                        return "当前没有活动地图";

                    var featureLayer = mapView.Map.FindLayers(layerName).OfType<FeatureLayer>().FirstOrDefault();
                    if (featureLayer == null)
                        return $"未找到要素图层：{layerName}";

                    var queryFilter = new QueryFilter { WhereClause = whereClause };
                    featureLayer.Select(queryFilter);

                    var selectionCount = featureLayer.GetSelection().GetCount();
                    return $"已选择 {selectionCount} 个要素";
                }
                catch (Exception ex)
                {
                    return $"按属性选择要素失败：{ex.Message}";
                }
            });
        }

        public async Task<string> CalculateFieldAsync(string layerName, string fieldName, string expression)
        {
            return await QueuedTask.Run(() =>
            {
                try
                {
                    var mapView = MapView.Active;
                    if (mapView?.Map == null)
                        return "当前没有活动地图";

                    var featureLayer = mapView.Map.FindLayers(layerName).OfType<FeatureLayer>().FirstOrDefault();
                    if (featureLayer == null)
                        return $"未找到要素图层：{layerName}";

                    // 这里应该实现字段计算逻辑
                    // 由于这是示例代码，我们返回一个模拟结果
                    return $"已为图层 {layerName} 的字段 {fieldName} 计算表达式：{expression}";
                }
                catch (Exception ex)
                {
                    return $"计算字段失败：{ex.Message}";
                }
            });
        }

        public async Task<string> ExportLayerAsync(string layerName, string outputPath, string format = "shapefile")
        {
            return await QueuedTask.Run(() =>
            {
                try
                {
                    var mapView = MapView.Active;
                    if (mapView?.Map == null)
                        return "当前没有活动地图";

                    var layer = mapView.Map.FindLayers(layerName).FirstOrDefault();
                    if (layer == null)
                        return $"未找到图层：{layerName}";

                    // 这里应该实现图层导出逻辑
                    // 由于这是示例代码，我们返回一个模拟结果
                    return $"已将图层 {layerName} 导出为 {format} 格式到：{outputPath}";
                }
                catch (Exception ex)
                {
                    return $"导出图层失败：{ex.Message}";
                }
            });
        }
    }
}
