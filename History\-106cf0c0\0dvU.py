#!/usr/bin/env python3
"""
Codeium设备重置工具
专门针对C:/Users/<USER>/.codeium目录的设备识别重置
"""

import os
import sys
import json
import uuid
import shutil
import time
from pathlib import Path
from typing import Dict, List, Optional
import logging

class CodeiumResetTool:
    """Codeium设备重置工具"""
    
    def __init__(self):
        self.logger = self._setup_logger()
        self.codeium_path = Path.home() / ".codeium"
        self.backup_dir = Path("codeium_backup")
        self.found_files = []
        
    def _setup_logger(self):
        """设置日志"""
        logger = logging.getLogger('CodeiumReset')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def scan_codeium_directory(self) -> Dict:
        """扫描Codeium目录"""
        self.logger.info(f"Scanning Codeium directory: {self.codeium_path}")
        
        scan_result = {
            'directory_exists': self.codeium_path.exists(),
            'total_files': 0,
            'device_related_files': [],
            'config_files': [],
            'log_files': [],
            'cache_files': [],
            'other_files': []
        }
        
        if not scan_result['directory_exists']:
            self.logger.warning(f"Codeium directory not found: {self.codeium_path}")
            return scan_result
        
        # 扫描所有文件
        for root, dirs, files in os.walk(self.codeium_path):
            for file in files:
                file_path = Path(root) / file
                relative_path = file_path.relative_to(self.codeium_path)
                scan_result['total_files'] += 1
                
                file_info = {
                    'path': str(file_path),
                    'relative_path': str(relative_path),
                    'size': file_path.stat().st_size if file_path.exists() else 0,
                    'modified': time.ctime(file_path.stat().st_mtime) if file_path.exists() else 'Unknown'
                }
                
                # 分类文件
                file_lower = file.lower()
                if any(keyword in file_lower for keyword in ['device', 'machine', 'session', 'uuid', 'guid']):
                    scan_result['device_related_files'].append(file_info)
                elif file_lower.endswith(('.json', '.config', '.cfg', '.ini')):
                    scan_result['config_files'].append(file_info)
                elif file_lower.endswith(('.log', '.txt')):
                    scan_result['log_files'].append(file_info)
                elif 'cache' in file_lower or 'temp' in file_lower:
                    scan_result['cache_files'].append(file_info)
                else:
                    scan_result['other_files'].append(file_info)
        
        self.logger.info(f"Found {scan_result['total_files']} files in Codeium directory")
        self.logger.info(f"Device-related files: {len(scan_result['device_related_files'])}")
        
        return scan_result
    
    def analyze_files_content(self, scan_result: Dict) -> Dict:
        """分析文件内容"""
        self.logger.info("Analyzing file contents for device identifiers...")
        
        analysis = {
            'files_with_device_ids': [],
            'files_with_machine_ids': [],
            'files_with_session_data': [],
            'suspicious_files': []
        }
        
        # 要搜索的模式
        device_patterns = [
            'deviceid', 'device_id', 'machineId', 'machine_id',
            'sessionId', 'session_id', 'uuid', 'guid'
        ]
        
        # 检查配置文件和其他可能包含设备信息的文件
        files_to_check = (scan_result['device_related_files'] + 
                         scan_result['config_files'] + 
                         scan_result['other_files'])
        
        for file_info in files_to_check:
            file_path = Path(file_info['path'])
            
            try:
                # 只检查小于1MB的文本文件
                if file_path.stat().st_size > 1024 * 1024:
                    continue
                
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read().lower()
                    
                    found_patterns = []
                    for pattern in device_patterns:
                        if pattern in content:
                            found_patterns.append(pattern)
                    
                    if found_patterns:
                        file_analysis = {
                            'file': file_info,
                            'patterns_found': found_patterns
                        }
                        
                        if any(p in ['deviceid', 'device_id'] for p in found_patterns):
                            analysis['files_with_device_ids'].append(file_analysis)
                        if any(p in ['machineid', 'machine_id'] for p in found_patterns):
                            analysis['files_with_machine_ids'].append(file_analysis)
                        if any(p in ['sessionid', 'session_id'] for p in found_patterns):
                            analysis['files_with_session_data'].append(file_analysis)
                        
                        analysis['suspicious_files'].append(file_analysis)
                        
            except Exception as e:
                self.logger.debug(f"Could not analyze {file_path}: {e}")
        
        return analysis
    
    def create_backup(self, scan_result: Dict) -> bool:
        """创建备份"""
        self.logger.info(f"Creating backup in {self.backup_dir}")
        
        try:
            # 创建备份目录
            self.backup_dir.mkdir(exist_ok=True)
            
            # 备份整个.codeium目录
            if self.codeium_path.exists():
                backup_codeium = self.backup_dir / "codeium_original"
                if backup_codeium.exists():
                    shutil.rmtree(backup_codeium)
                
                shutil.copytree(self.codeium_path, backup_codeium)
                self.logger.info(f"Backed up Codeium directory to {backup_codeium}")
            
            # 保存扫描结果
            with open(self.backup_dir / "scan_result.json", 'w') as f:
                json.dump(scan_result, f, indent=2)
            
            # 创建恢复脚本
            restore_script = self.backup_dir / "restore.py"
            with open(restore_script, 'w') as f:
                f.write(f'''#!/usr/bin/env python3
"""
Codeium恢复脚本
"""
import shutil
from pathlib import Path

def restore():
    backup_dir = Path("{self.backup_dir}")
    codeium_path = Path("{self.codeium_path}")
    backup_codeium = backup_dir / "codeium_original"
    
    if backup_codeium.exists():
        if codeium_path.exists():
            shutil.rmtree(codeium_path)
        shutil.copytree(backup_codeium, codeium_path)
        print(f"Restored Codeium directory from backup")
    else:
        print("Backup not found")

if __name__ == "__main__":
    restore()
''')
            
            self.logger.info("Backup completed successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Backup failed: {e}")
            return False
    
    def reset_codeium_identity(self) -> bool:
        """重置Codeium身份"""
        self.logger.info("Resetting Codeium identity...")
        
        try:
            if self.codeium_path.exists():
                # 删除整个.codeium目录
                shutil.rmtree(self.codeium_path)
                self.logger.info(f"Deleted Codeium directory: {self.codeium_path}")
                
                # 等待一下确保删除完成
                time.sleep(2)
                
                # 重新创建空目录
                self.codeium_path.mkdir(exist_ok=True)
                self.logger.info(f"Created new empty Codeium directory")
                
                return True
            else:
                self.logger.warning("Codeium directory does not exist")
                return True
                
        except Exception as e:
            self.logger.error(f"Failed to reset Codeium identity: {e}")
            return False
    
    def verify_reset(self) -> Dict:
        """验证重置结果"""
        self.logger.info("Verifying reset results...")
        
        verification = {
            'directory_exists': self.codeium_path.exists(),
            'is_empty': True,
            'file_count': 0,
            'reset_successful': False
        }
        
        if verification['directory_exists']:
            # 检查目录是否为空或只包含新文件
            file_count = 0
            for root, dirs, files in os.walk(self.codeium_path):
                file_count += len(files)
            
            verification['file_count'] = file_count
            verification['is_empty'] = file_count == 0
            verification['reset_successful'] = file_count <= 5  # 允许少量新生成的文件
        else:
            verification['reset_successful'] = True  # 目录不存在也算成功
        
        return verification

def main():
    """主函数"""
    print("Codeium设备重置工具")
    print("=" * 40)
    print("此工具专门用于重置Codeium的设备识别信息")
    print(f"目标目录: {Path.home() / '.codeium'}")
    print()
    
    tool = CodeiumResetTool()
    
    # 1. 扫描目录
    print("1. 扫描Codeium目录...")
    scan_result = tool.scan_codeium_directory()
    
    if not scan_result['directory_exists']:
        print("   Codeium目录不存在，无需重置")
        return
    
    print(f"   找到 {scan_result['total_files']} 个文件")
    print(f"   设备相关文件: {len(scan_result['device_related_files'])}")
    print(f"   配置文件: {len(scan_result['config_files'])}")
    print(f"   日志文件: {len(scan_result['log_files'])}")
    
    # 2. 分析文件内容
    print("\n2. 分析文件内容...")
    analysis = tool.analyze_files_content(scan_result)
    print(f"   包含设备ID的文件: {len(analysis['files_with_device_ids'])}")
    print(f"   包含机器ID的文件: {len(analysis['files_with_machine_ids'])}")
    print(f"   包含会话数据的文件: {len(analysis['files_with_session_data'])}")
    print(f"   可疑文件总数: {len(analysis['suspicious_files'])}")
    
    # 显示可疑文件
    if analysis['suspicious_files']:
        print("\n   可疑文件列表:")
        for i, file_analysis in enumerate(analysis['suspicious_files'][:5], 1):
            file_info = file_analysis['file']
            patterns = ', '.join(file_analysis['patterns_found'])
            print(f"     {i}. {file_info['relative_path']} (包含: {patterns})")
        
        if len(analysis['suspicious_files']) > 5:
            print(f"     ... 还有 {len(analysis['suspicious_files']) - 5} 个文件")
    
    # 3. 创建备份
    print("\n3. 创建备份...")
    if tool.create_backup(scan_result):
        print("   备份完成")
    else:
        print("   备份失败，停止操作")
        return
    
    # 4. 确认重置
    print("\n4. 准备重置Codeium身份...")
    print("   ⚠️  警告: 这将删除整个.codeium目录")
    print("   ⚠️  这可能会影响Codeium的所有设置和缓存")
    print("   ⚠️  备份已创建，可以使用restore.py恢复")
    
    response = input("\n   确认要重置Codeium身份吗? (yes/no): ")
    if response.lower() != 'yes':
        print("   操作已取消")
        return
    
    # 5. 执行重置
    print("\n5. 执行重置...")
    if tool.reset_codeium_identity():
        print("   重置完成")
    else:
        print("   重置失败")
        return
    
    # 6. 验证重置
    print("\n6. 验证重置结果...")
    verification = tool.verify_reset()
    
    if verification['reset_successful']:
        print("   ✅ 重置成功")
        print(f"   目录存在: {'是' if verification['directory_exists'] else '否'}")
        print(f"   文件数量: {verification['file_count']}")
    else:
        print("   ❌ 重置可能不完整")
        print(f"   仍有 {verification['file_count']} 个文件")
    
    print(f"\n重置完成!")
    print(f"备份保存在: {tool.backup_dir}")
    print(f"如需恢复，请运行: python {tool.backup_dir}/restore.py")
    print(f"现在可以重启Windsurf测试新设备检测")

if __name__ == "__main__":
    main()
