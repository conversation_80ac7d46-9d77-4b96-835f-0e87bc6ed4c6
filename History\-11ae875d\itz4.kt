package com.example.surveyassistant.ui.theme

import androidx.compose.ui.graphics.Color

// 原有颜色保留
val Purple80 = Color(0xFFD0BCFF)
val PurpleGrey80 = Color(0xFFCCC2DC)
val Pink80 = Color(0xFFEFB8C8)

val Purple40 = Color(0xFF6650a4)
val PurpleGrey40 = Color(0xFF625b71)
val Pink40 = Color(0xFF7D5260)

// 蓝白主题颜色
val PrimaryBlue = Color(0xFF2196F3)
val PrimaryBlueDark = Color(0xFF1976D2)
val PrimaryBlueLight = Color(0xFFBBDEFB)

val AccentBlue = Color(0xFF03DAC6)
val SurfaceBlue = Color(0xFFF5F9FF)

val BackgroundLight = Color(0xFFFAFAFA)
val BackgroundWhite = Color(0xFFFFFFFF)

val TextPrimary = Color(0xFF212121)
val TextSecondary = Color(0xFF757575)
val TextHint = Color(0xFFBDBDBD)

val SuccessGreen = Color(0xFF4CAF50)
val WarningOrange = Color(0xFFFF9800)
val ErrorRed = Color(0xFFF44336)

val OverlayDark = Color(0x80000000)
val OverlayLight = Color(0x40FFFFFF)