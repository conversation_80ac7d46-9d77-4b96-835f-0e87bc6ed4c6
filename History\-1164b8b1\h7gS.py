#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终验证脚本
检查Windsurf设备重置和存储修复的完整状态
"""

import os
import json
from pathlib import Path

def final_verification():
    """最终验证所有功能"""
    print("🔍 最终验证Windsurf状态...")
    print("=" * 60)
    
    windsurf_path = Path("d:/RUANJIAN/Windsurf")
    user_profile = Path(os.environ.get('USERPROFILE', ''))
    appdata_windsurf = Path(os.environ.get('APPDATA', '')) / "Windsurf"
    
    success_count = 0
    total_checks = 0
    
    # 1. 检查设备ID固定
    print("\n🆔 1. 检查设备ID固定:")
    
    device_files = [
        windsurf_path / "resources/app/node_modules/@vscode/deviceid/dist/devdeviceid.js",
        windsurf_path / "resources/app/node_modules/@vscode/deviceid/dist/storage.js"
    ]
    
    for device_file in device_files:
        total_checks += 1
        if device_file.exists():
            with open(device_file, 'r', encoding='utf-8') as f:
                content = f.read()
                if "f20a703d-4462-411d-80ce-370eb7067285" in content:
                    print(f"   ✅ {device_file.name} - 设备ID已固定")
                    success_count += 1
                else:
                    print(f"   ❌ {device_file.name} - 设备ID未正确固定")
        else:
            print(f"   ❓ {device_file.name} - 文件不存在")
    
    # 2. 检查存储功能
    print("\n💾 2. 检查存储功能:")
    
    storage_dirs = [
        appdata_windsurf / "User" / "globalStorage",
        appdata_windsurf / "User" / "workspaceStorage",
        appdata_windsurf / "Local Storage"
    ]
    
    for storage_dir in storage_dirs:
        total_checks += 1
        if storage_dir.exists():
            print(f"   ✅ {storage_dir.name} - 存储目录存在")
            success_count += 1
        else:
            print(f"   ❌ {storage_dir.name} - 存储目录不存在")
    
    # 3. 检查设备标识符配置
    print("\n🔧 3. 检查设备标识符配置:")
    
    # 检查globalStorage
    total_checks += 1
    global_storage_file = appdata_windsurf / "User" / "globalStorage" / "storage.json"
    if global_storage_file.exists():
        try:
            with open(global_storage_file, 'r', encoding='utf-8') as f:
                storage_data = json.load(f)
            
            expected_device_id = "f20a703d-4462-411d-80ce-370eb7067285"
            if storage_data.get("telemetry.devDeviceId") == expected_device_id:
                print(f"   ✅ globalStorage - 设备ID正确")
                success_count += 1
            else:
                print(f"   ❌ globalStorage - 设备ID不正确")
        except Exception as e:
            print(f"   ❌ globalStorage - 读取失败: {e}")
    else:
        print(f"   ❌ globalStorage - 文件不存在")
    
    # 检查machineid
    total_checks += 1
    machineid_file = appdata_windsurf / "machineid"
    if machineid_file.exists():
        try:
            with open(machineid_file, 'r', encoding='utf-8') as f:
                machine_id = f.read().strip()
            if machine_id == "f20a703d-4462-411d-80ce-370eb7067285":
                print(f"   ✅ machineid - 设备ID正确")
                success_count += 1
            else:
                print(f"   ❌ machineid - 设备ID不正确: {machine_id}")
        except Exception as e:
            print(f"   ❌ machineid - 读取失败: {e}")
    else:
        print(f"   ❌ machineid - 文件不存在")
    
    # 4. 检查原生模块禁用
    print("\n🚫 4. 检查原生模块禁用:")
    
    total_checks += 1
    windows_node = windsurf_path / "resources/app/node_modules/@vscode/deviceid/build/Release/windows.node"
    if windows_node.exists():
        file_size = windows_node.stat().st_size
        if file_size <= 1024:
            print(f"   ✅ windows.node - 已禁用 (文件大小: {file_size} 字节)")
            success_count += 1
        else:
            print(f"   ❌ windows.node - 未禁用 (文件大小: {file_size} 字节)")
    else:
        print(f"   ❓ windows.node - 文件不存在")
    
    # 5. 显示当前配置
    print("\n📋 5. 当前配置总结:")
    print(f"   🆔 固定设备ID: f20a703d-4462-411d-80ce-370eb7067285")
    print(f"   💥 崩溃报告ID: 29a6499c-5d46-4d99-ba49-41e1b0e76527")
    print(f"   💻 机器ID: 33383639393437373663643634306362616438653265633432323962313231643")
    print(f"   📊 SQM ID: {{22BDEA9C-8A91-4171-9C3A-B2244A8C9601}}")
    
    # 6. 总结
    print("\n" + "=" * 60)
    print(f"📊 验证结果: {success_count}/{total_checks} 项检查通过")
    
    if success_count == total_checks:
        print("🎉 所有检查通过！配置完美！")
        print("\n✅ 功能状态:")
        print("   🔒 设备ID已固定 - 绕过设备检测")
        print("   💾 存储功能正常 - 可以保存登录数据")
        print("   🚫 原生模块已禁用 - 阻止注册表存储")
        print("\n🚀 现在可以:")
        print("   1. 正常登录和保存用户数据")
        print("   2. 切换账号而不被识别为同一设备")
        print("   3. 享受完整的Windsurf功能")
    else:
        print("⚠️ 部分检查未通过，可能需要重新运行相关脚本")
    
    return success_count == total_checks

if __name__ == "__main__":
    try:
        final_verification()
    except Exception as e:
        print(f"❌ 验证脚本执行失败: {e}")
        import traceback
        traceback.print_exc()
