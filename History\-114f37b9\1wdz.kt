package com.example.babylog.ui.screens

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.navigation.NavController
import com.example.babylog.data.database.BabyLogDatabase
import com.example.babylog.data.entity.Baby
import com.example.babylog.data.repository.BabyRepository
import com.example.babylog.navigation.Screen
import com.example.babylog.ui.theme.*
import java.text.SimpleDateFormat
import java.util.*

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun HomeScreen(navController: NavController) {
    val context = LocalContext.current
    val database = BabyLogDatabase.getDatabase(context)
    val repository = BabyRepository(
        database.babyDao(),
        database.healthRecordDao(),
        database.milestoneDao(),
        database.feedingRecordDao(),
        database.photoDao()
    )

    val babies by repository.getAllBabies().collectAsState(initial = emptyList())
    var showAddDialog by remember { mutableStateOf(false) }

    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(NeumorphismColors.background)
    ) {
        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = 20.dp),
            contentPadding = PaddingValues(vertical = 20.dp),
            verticalArrangement = Arrangement.spacedBy(20.dp)
        ) {
            // Header
            item {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Column {
                        Text(
                            text = "宝贝日记",
                            style = MaterialTheme.typography.headlineLarge,
                            fontWeight = FontWeight.Bold,
                            color = NeumorphismColors.textPrimary
                        )
                        Text(
                            text = "记录每一个珍贵时刻",
                            style = MaterialTheme.typography.bodyMedium,
                            color = NeumorphismColors.textSecondary
                        )
                    }
                }
            }
        
        Spacer(modifier = Modifier.height(16.dp))
        
        if (babies.isEmpty()) {
            // Empty state
            Card(
                modifier = Modifier.fillMaxWidth(),
                onClick = { navController.navigate("${Screen.AddBaby.route}?babyId=") }
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(24.dp),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Icon(
                        Icons.Default.Add,
                        contentDescription = null,
                        modifier = Modifier.size(48.dp),
                        tint = MaterialTheme.colorScheme.primary
                    )
                    Spacer(modifier = Modifier.height(16.dp))
                    Text(
                        text = "添加第一个宝宝档案",
                        style = MaterialTheme.typography.titleMedium
                    )
                    Text(
                        text = "开始记录宝宝的成长历程",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        } else {
            // Baby list
            LazyColumn(
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                items(babies) { baby ->
                    BabyCard(
                        baby = baby,
                        onClick = { 
                            // Navigate to baby details or set as current baby
                        }
                    )
                }
                
                item {
                    Card(
                        modifier = Modifier.fillMaxWidth(),
                        onClick = { navController.navigate("${Screen.AddBaby.route}?babyId=") }
                    ) {
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(16.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Icon(
                                Icons.Default.Add,
                                contentDescription = null,
                                tint = MaterialTheme.colorScheme.primary
                            )
                            Spacer(modifier = Modifier.width(16.dp))
                            Text(
                                text = "添加新宝宝",
                                style = MaterialTheme.typography.titleMedium
                            )
                        }
                    }
                }
            }
        }
    }
}

@Composable
fun BabyCard(
    baby: Baby,
    onClick: () -> Unit
) {
    val dateFormat = SimpleDateFormat("yyyy年MM月dd日", Locale.getDefault())
    val ageInDays = ((Date().time - baby.birthDate.time) / (1000 * 60 * 60 * 24)).toInt()
    val ageText = when {
        ageInDays < 30 -> "${ageInDays}天"
        ageInDays < 365 -> "${ageInDays / 30}个月"
        else -> "${ageInDays / 365}岁${(ageInDays % 365) / 30}个月"
    }
    
    Card(
        modifier = Modifier.fillMaxWidth(),
        onClick = onClick
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column {
                    Text(
                        text = baby.name,
                        style = MaterialTheme.typography.titleLarge,
                        fontWeight = FontWeight.Bold
                    )
                    Text(
                        text = "出生日期: ${dateFormat.format(baby.birthDate)}",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    Text(
                        text = "年龄: $ageText",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
                
                Icon(
                    if (baby.gender == "male") Icons.Default.Person else Icons.Default.Person,
                    contentDescription = baby.gender,
                    modifier = Modifier.size(32.dp),
                    tint = if (baby.gender == "male")
                        MaterialTheme.colorScheme.primary
                    else
                        MaterialTheme.colorScheme.secondary
                )
            }
        }
    }
}
