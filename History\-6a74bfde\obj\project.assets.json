{"version": 3, "targets": {"net8.0-windows7.0": {"DuckDB.NET.Bindings.Full/1.2.0": {"type": "package", "compile": {"lib/net8.0/DuckDB.NET.Bindings.dll": {"related": ".pdb"}}, "runtime": {"lib/net8.0/DuckDB.NET.Bindings.dll": {"related": ".pdb"}}, "runtimeTargets": {"runtimes/linux-arm64/native/libduckdb.so": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-x64/native/libduckdb.so": {"assetType": "native", "rid": "linux-x64"}, "runtimes/osx/native/libduckdb.dylib": {"assetType": "native", "rid": "osx"}, "runtimes/win-arm64/native/duckdb.dll": {"assetType": "native", "rid": "win-arm64"}, "runtimes/win-x64/native/duckdb.dll": {"assetType": "native", "rid": "win-x64"}}}, "DuckDB.NET.Data.Full/1.2.0": {"type": "package", "dependencies": {"DuckDB.NET.Bindings.Full": "1.2.0"}, "compile": {"lib/net8.0/DuckDB.NET.Data.dll": {"related": ".pdb"}}, "runtime": {"lib/net8.0/DuckDB.NET.Data.dll": {"related": ".pdb"}}}, "Esri.ArcGISRuntime/200.6.0": {"type": "package", "dependencies": {"Esri.ArcGISRuntime.runtimes.win": "200.6.0"}, "compile": {"lib/net8.0/Esri.ArcGISRuntime.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Esri.ArcGISRuntime.dll": {"related": ".xml"}}, "resource": {"lib/net8.0/ar/Esri.ArcGISRuntime.resources.dll": {"locale": "ar"}, "lib/net8.0/bg/Esri.ArcGISRuntime.resources.dll": {"locale": "bg"}, "lib/net8.0/bs/Esri.ArcGISRuntime.resources.dll": {"locale": "bs"}, "lib/net8.0/ca/Esri.ArcGISRuntime.resources.dll": {"locale": "ca"}, "lib/net8.0/cs/Esri.ArcGISRuntime.resources.dll": {"locale": "cs"}, "lib/net8.0/da/Esri.ArcGISRuntime.resources.dll": {"locale": "da"}, "lib/net8.0/de/Esri.ArcGISRuntime.resources.dll": {"locale": "de"}, "lib/net8.0/el/Esri.ArcGISRuntime.resources.dll": {"locale": "el"}, "lib/net8.0/es/Esri.ArcGISRuntime.resources.dll": {"locale": "es"}, "lib/net8.0/et/Esri.ArcGISRuntime.resources.dll": {"locale": "et"}, "lib/net8.0/fi/Esri.ArcGISRuntime.resources.dll": {"locale": "fi"}, "lib/net8.0/fr/Esri.ArcGISRuntime.resources.dll": {"locale": "fr"}, "lib/net8.0/he/Esri.ArcGISRuntime.resources.dll": {"locale": "he"}, "lib/net8.0/hr/Esri.ArcGISRuntime.resources.dll": {"locale": "hr"}, "lib/net8.0/hu/Esri.ArcGISRuntime.resources.dll": {"locale": "hu"}, "lib/net8.0/id/Esri.ArcGISRuntime.resources.dll": {"locale": "id"}, "lib/net8.0/it/Esri.ArcGISRuntime.resources.dll": {"locale": "it"}, "lib/net8.0/ja/Esri.ArcGISRuntime.resources.dll": {"locale": "ja"}, "lib/net8.0/ko/Esri.ArcGISRuntime.resources.dll": {"locale": "ko"}, "lib/net8.0/lt/Esri.ArcGISRuntime.resources.dll": {"locale": "lt"}, "lib/net8.0/lv/Esri.ArcGISRuntime.resources.dll": {"locale": "lv"}, "lib/net8.0/nb-NO/Esri.ArcGISRuntime.resources.dll": {"locale": "nb-NO"}, "lib/net8.0/nl/Esri.ArcGISRuntime.resources.dll": {"locale": "nl"}, "lib/net8.0/no/Esri.ArcGISRuntime.resources.dll": {"locale": "no"}, "lib/net8.0/pl/Esri.ArcGISRuntime.resources.dll": {"locale": "pl"}, "lib/net8.0/pt-BR/Esri.ArcGISRuntime.resources.dll": {"locale": "pt-BR"}, "lib/net8.0/pt-PT/Esri.ArcGISRuntime.resources.dll": {"locale": "pt-PT"}, "lib/net8.0/ro/Esri.ArcGISRuntime.resources.dll": {"locale": "ro"}, "lib/net8.0/ru/Esri.ArcGISRuntime.resources.dll": {"locale": "ru"}, "lib/net8.0/sk/Esri.ArcGISRuntime.resources.dll": {"locale": "sk"}, "lib/net8.0/sl/Esri.ArcGISRuntime.resources.dll": {"locale": "sl"}, "lib/net8.0/sr/Esri.ArcGISRuntime.resources.dll": {"locale": "sr"}, "lib/net8.0/sv/Esri.ArcGISRuntime.resources.dll": {"locale": "sv"}, "lib/net8.0/th/Esri.ArcGISRuntime.resources.dll": {"locale": "th"}, "lib/net8.0/tr/Esri.ArcGISRuntime.resources.dll": {"locale": "tr"}, "lib/net8.0/uk/Esri.ArcGISRuntime.resources.dll": {"locale": "uk"}, "lib/net8.0/vi/Esri.ArcGISRuntime.resources.dll": {"locale": "vi"}, "lib/net8.0/zh-CN/Esri.ArcGISRuntime.resources.dll": {"locale": "zh-CN"}, "lib/net8.0/zh-HK/Esri.ArcGISRuntime.resources.dll": {"locale": "zh-HK"}, "lib/net8.0/zh-Hans/Esri.ArcGISRuntime.resources.dll": {"locale": "zh-Hans"}, "lib/net8.0/zh-TW/Esri.ArcGISRuntime.resources.dll": {"locale": "zh-TW"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Esri.ArcGISRuntime.runtimes.win/200.6.0": {"type": "package", "build": {"buildTransitive/netstandard1.0/_._": {}}, "runtimeTargets": {"runtimes/win-arm64/native/RuntimeCoreNet200_6.dll": {"assetType": "native", "rid": "win-arm64"}, "runtimes/win-arm64/native/runtimecore.dll": {"assetType": "native", "rid": "win-arm64"}, "runtimes/win-arm64/native/runtimecoreAssembly.manifest": {"assetType": "native", "rid": "win-arm64"}, "runtimes/win-x64/native/RuntimeCoreNet200_6.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x64/native/runtimecore.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x64/native/runtimecoreAssembly.manifest": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x86/native/RuntimeCoreNet200_6.dll": {"assetType": "native", "rid": "win-x86"}, "runtimes/win-x86/native/runtimecore.dll": {"assetType": "native", "rid": "win-x86"}, "runtimes/win-x86/native/runtimecoreAssembly.manifest": {"assetType": "native", "rid": "win-x86"}}}, "Microsoft.Web.WebView2/1.0.3296.44": {"type": "package", "build": {"buildTransitive/Microsoft.Web.WebView2.targets": {}}, "runtimeTargets": {"runtimes/win-arm64/native/WebView2Loader.dll": {"assetType": "native", "rid": "win-arm64"}, "runtimes/win-x64/native/WebView2Loader.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x86/native/WebView2Loader.dll": {"assetType": "native", "rid": "win-x86"}}}, "Newtonsoft.Json/13.0.3": {"type": "package", "compile": {"lib/net6.0/Newtonsoft.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"related": ".xml"}}}, "System.Text.Encoding.CodePages/8.0.0": {"type": "package", "compile": {"lib/net8.0/System.Text.Encoding.CodePages.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Text.Encoding.CodePages.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net8.0/System.Text.Encoding.CodePages.dll": {"assetType": "runtime", "rid": "win"}}}}, "net8.0-windows7.0/win-x64": {"DuckDB.NET.Bindings.Full/1.2.0": {"type": "package", "compile": {"lib/net8.0/DuckDB.NET.Bindings.dll": {"related": ".pdb"}}, "runtime": {"lib/net8.0/DuckDB.NET.Bindings.dll": {"related": ".pdb"}}, "native": {"runtimes/win-x64/native/duckdb.dll": {}}}, "DuckDB.NET.Data.Full/1.2.0": {"type": "package", "dependencies": {"DuckDB.NET.Bindings.Full": "1.2.0"}, "compile": {"lib/net8.0/DuckDB.NET.Data.dll": {"related": ".pdb"}}, "runtime": {"lib/net8.0/DuckDB.NET.Data.dll": {"related": ".pdb"}}}, "Esri.ArcGISRuntime/200.6.0": {"type": "package", "dependencies": {"Esri.ArcGISRuntime.runtimes.win": "200.6.0"}, "compile": {"lib/net8.0/Esri.ArcGISRuntime.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Esri.ArcGISRuntime.dll": {"related": ".xml"}}, "resource": {"lib/net8.0/ar/Esri.ArcGISRuntime.resources.dll": {"locale": "ar"}, "lib/net8.0/bg/Esri.ArcGISRuntime.resources.dll": {"locale": "bg"}, "lib/net8.0/bs/Esri.ArcGISRuntime.resources.dll": {"locale": "bs"}, "lib/net8.0/ca/Esri.ArcGISRuntime.resources.dll": {"locale": "ca"}, "lib/net8.0/cs/Esri.ArcGISRuntime.resources.dll": {"locale": "cs"}, "lib/net8.0/da/Esri.ArcGISRuntime.resources.dll": {"locale": "da"}, "lib/net8.0/de/Esri.ArcGISRuntime.resources.dll": {"locale": "de"}, "lib/net8.0/el/Esri.ArcGISRuntime.resources.dll": {"locale": "el"}, "lib/net8.0/es/Esri.ArcGISRuntime.resources.dll": {"locale": "es"}, "lib/net8.0/et/Esri.ArcGISRuntime.resources.dll": {"locale": "et"}, "lib/net8.0/fi/Esri.ArcGISRuntime.resources.dll": {"locale": "fi"}, "lib/net8.0/fr/Esri.ArcGISRuntime.resources.dll": {"locale": "fr"}, "lib/net8.0/he/Esri.ArcGISRuntime.resources.dll": {"locale": "he"}, "lib/net8.0/hr/Esri.ArcGISRuntime.resources.dll": {"locale": "hr"}, "lib/net8.0/hu/Esri.ArcGISRuntime.resources.dll": {"locale": "hu"}, "lib/net8.0/id/Esri.ArcGISRuntime.resources.dll": {"locale": "id"}, "lib/net8.0/it/Esri.ArcGISRuntime.resources.dll": {"locale": "it"}, "lib/net8.0/ja/Esri.ArcGISRuntime.resources.dll": {"locale": "ja"}, "lib/net8.0/ko/Esri.ArcGISRuntime.resources.dll": {"locale": "ko"}, "lib/net8.0/lt/Esri.ArcGISRuntime.resources.dll": {"locale": "lt"}, "lib/net8.0/lv/Esri.ArcGISRuntime.resources.dll": {"locale": "lv"}, "lib/net8.0/nb-NO/Esri.ArcGISRuntime.resources.dll": {"locale": "nb-NO"}, "lib/net8.0/nl/Esri.ArcGISRuntime.resources.dll": {"locale": "nl"}, "lib/net8.0/no/Esri.ArcGISRuntime.resources.dll": {"locale": "no"}, "lib/net8.0/pl/Esri.ArcGISRuntime.resources.dll": {"locale": "pl"}, "lib/net8.0/pt-BR/Esri.ArcGISRuntime.resources.dll": {"locale": "pt-BR"}, "lib/net8.0/pt-PT/Esri.ArcGISRuntime.resources.dll": {"locale": "pt-PT"}, "lib/net8.0/ro/Esri.ArcGISRuntime.resources.dll": {"locale": "ro"}, "lib/net8.0/ru/Esri.ArcGISRuntime.resources.dll": {"locale": "ru"}, "lib/net8.0/sk/Esri.ArcGISRuntime.resources.dll": {"locale": "sk"}, "lib/net8.0/sl/Esri.ArcGISRuntime.resources.dll": {"locale": "sl"}, "lib/net8.0/sr/Esri.ArcGISRuntime.resources.dll": {"locale": "sr"}, "lib/net8.0/sv/Esri.ArcGISRuntime.resources.dll": {"locale": "sv"}, "lib/net8.0/th/Esri.ArcGISRuntime.resources.dll": {"locale": "th"}, "lib/net8.0/tr/Esri.ArcGISRuntime.resources.dll": {"locale": "tr"}, "lib/net8.0/uk/Esri.ArcGISRuntime.resources.dll": {"locale": "uk"}, "lib/net8.0/vi/Esri.ArcGISRuntime.resources.dll": {"locale": "vi"}, "lib/net8.0/zh-CN/Esri.ArcGISRuntime.resources.dll": {"locale": "zh-CN"}, "lib/net8.0/zh-HK/Esri.ArcGISRuntime.resources.dll": {"locale": "zh-HK"}, "lib/net8.0/zh-Hans/Esri.ArcGISRuntime.resources.dll": {"locale": "zh-Hans"}, "lib/net8.0/zh-TW/Esri.ArcGISRuntime.resources.dll": {"locale": "zh-TW"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Esri.ArcGISRuntime.runtimes.win/200.6.0": {"type": "package", "native": {"runtimes/win-x64/native/RuntimeCoreNet200_6.dll": {}, "runtimes/win-x64/native/runtimecore.dll": {}, "runtimes/win-x64/native/runtimecoreAssembly.manifest": {}}, "build": {"buildTransitive/netstandard1.0/_._": {}}}, "Microsoft.Web.WebView2/1.0.3296.44": {"type": "package", "native": {"runtimes/win-x64/native/WebView2Loader.dll": {}}, "build": {"buildTransitive/Microsoft.Web.WebView2.targets": {}}}, "Newtonsoft.Json/13.0.3": {"type": "package", "compile": {"lib/net6.0/Newtonsoft.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"related": ".xml"}}}, "System.Text.Encoding.CodePages/8.0.0": {"type": "package", "compile": {"lib/net8.0/System.Text.Encoding.CodePages.dll": {"related": ".xml"}}, "runtime": {"runtimes/win/lib/net8.0/System.Text.Encoding.CodePages.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}}}, "libraries": {"DuckDB.NET.Bindings.Full/1.2.0": {"sha512": "lS4E5kz8v1qhxxQZKJCQoZ0z/QlJtSpRsdRZifIw2XYrfwF/bOzro3oXSS1bC+IpqwJWqFet3kL17d4Hsefg1Q==", "type": "package", "path": "duckdb.net.bindings.full/1.2.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE-DuckDB.txt", "LICENSE.md", "Logo.jpg", "README.md", "duckdb.net.bindings.full.1.2.0.nupkg.sha512", "duckdb.net.bindings.full.nuspec", "lib/net6.0/DuckDB.NET.Bindings.dll", "lib/net6.0/DuckDB.NET.Bindings.pdb", "lib/net8.0/DuckDB.NET.Bindings.dll", "lib/net8.0/DuckDB.NET.Bindings.pdb", "lib/netstandard2.0/DuckDB.NET.Bindings.dll", "lib/netstandard2.0/DuckDB.NET.Bindings.pdb", "runtimes/linux-arm64/native/libduckdb.so", "runtimes/linux-x64/native/libduckdb.so", "runtimes/osx/native/libduckdb.dylib", "runtimes/win-arm64/native/duckdb.dll", "runtimes/win-x64/native/duckdb.dll"]}, "DuckDB.NET.Data.Full/1.2.0": {"sha512": "HeA+W2SVeVxMh/r+ngMVhgPwbtYZGIOIw++pOgV+5BMG2lk/V49tRFijNDVqCizjIaJyuxTBc1dcbbbnb7mJtg==", "type": "package", "path": "duckdb.net.data.full/1.2.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE-DuckDB.txt", "LICENSE.md", "Logo.jpg", "README.md", "duckdb.net.data.full.1.2.0.nupkg.sha512", "duckdb.net.data.full.nuspec", "lib/net6.0/DuckDB.NET.Data.dll", "lib/net6.0/DuckDB.NET.Data.pdb", "lib/net8.0/DuckDB.NET.Data.dll", "lib/net8.0/DuckDB.NET.Data.pdb", "lib/netstandard2.0/DuckDB.NET.Data.dll", "lib/netstandard2.0/DuckDB.NET.Data.pdb"]}, "Esri.ArcGISRuntime/200.6.0": {"sha512": "5HdnM9oDpp2d2j4jP8WXMOIDgaTSoTIygKc0y1vYqzrn9tVZq8xoJqDKuGcHbbzhr4Sjx1qnngfVK02QqIfYaQ==", "type": "package", "path": "esri.arcgisruntime/200.6.0", "files": [".nupkg.metadata", ".signature.p7s", "build/MonoAndroid10/Esri.ArcGISRuntime.targets", "build/Xamarin.iOS10/Esri.ArcGISRuntime.targets", "build/net472/Esri.ArcGISRuntime.targets", "build/net6.0-android1.0/Esri.ArcGISRuntime.targets", "build/net6.0-ios1.0/Esri.ArcGISRuntime.targets", "build/net6.0-maccatalyst1.0/Esri.ArcGISRuntime.targets", "build/net6.0-windows7.0/Esri.ArcGISRuntime.targets", "build/net8.0-android34.0/Esri.ArcGISRuntime.targets", "build/net8.0-ios17.0/Esri.ArcGISRuntime.targets", "build/net8.0-maccatalyst17.0/Esri.ArcGISRuntime.targets", "build/net8.0-windows10.0.19041.0/Esri.ArcGISRuntime.targets", "build/net8.0/_._", "build/netcoreapp/Esri.ArcGISRuntime.targets", "build/uap10.0/Esri.ArcGISRuntime.targets", "buildTransitive/MonoAndroid10/Esri.ArcGISRuntime.targets", "buildTransitive/Xamarin.iOS10/Esri.ArcGISRuntime.targets", "buildTransitive/net472/Esri.ArcGISRuntime.targets", "buildTransitive/net6.0-android1.0/Esri.ArcGISRuntime.targets", "buildTransitive/net6.0-ios1.0/Esri.ArcGISRuntime.targets", "buildTransitive/net6.0-maccatalyst1.0/Esri.ArcGISRuntime.targets", "buildTransitive/net6.0-windows7.0/Esri.ArcGISRuntime.targets", "buildTransitive/net8.0-android34.0/Esri.ArcGISRuntime.targets", "buildTransitive/net8.0-ios17.0/Esri.ArcGISRuntime.targets", "buildTransitive/net8.0-maccatalyst17.0/Esri.ArcGISRuntime.targets", "buildTransitive/net8.0-windows10.0.19041.0/Esri.ArcGISRuntime.targets", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp/Esri.ArcGISRuntime.targets", "buildTransitive/uap10.0/Esri.ArcGISRuntime.targets", "esri.arcgisruntime.200.6.0.nupkg.sha512", "esri.arcgisruntime.nuspec", "icon.png", "legal/Copyright_and_Trademarks.pdf", "legal/eula.txt", "lib/net472/Esri.ArcGISRuntime.dll", "lib/net472/Esri.ArcGISRuntime.xml", "lib/net472/ar/Esri.ArcGISRuntime.resources.dll", "lib/net472/bg/Esri.ArcGISRuntime.resources.dll", "lib/net472/bs/Esri.ArcGISRuntime.resources.dll", "lib/net472/ca/Esri.ArcGISRuntime.resources.dll", "lib/net472/cs/Esri.ArcGISRuntime.resources.dll", "lib/net472/da/Esri.ArcGISRuntime.resources.dll", "lib/net472/de/Esri.ArcGISRuntime.resources.dll", "lib/net472/el/Esri.ArcGISRuntime.resources.dll", "lib/net472/es/Esri.ArcGISRuntime.resources.dll", "lib/net472/et/Esri.ArcGISRuntime.resources.dll", "lib/net472/fi/Esri.ArcGISRuntime.resources.dll", "lib/net472/fr/Esri.ArcGISRuntime.resources.dll", "lib/net472/he/Esri.ArcGISRuntime.resources.dll", "lib/net472/hr/Esri.ArcGISRuntime.resources.dll", "lib/net472/hu/Esri.ArcGISRuntime.resources.dll", "lib/net472/id/Esri.ArcGISRuntime.resources.dll", "lib/net472/it/Esri.ArcGISRuntime.resources.dll", "lib/net472/ja/Esri.ArcGISRuntime.resources.dll", "lib/net472/ko/Esri.ArcGISRuntime.resources.dll", "lib/net472/lt/Esri.ArcGISRuntime.resources.dll", "lib/net472/lv/Esri.ArcGISRuntime.resources.dll", "lib/net472/nb-NO/Esri.ArcGISRuntime.resources.dll", "lib/net472/nl/Esri.ArcGISRuntime.resources.dll", "lib/net472/no/Esri.ArcGISRuntime.resources.dll", "lib/net472/pl/Esri.ArcGISRuntime.resources.dll", "lib/net472/pt-BR/Esri.ArcGISRuntime.resources.dll", "lib/net472/pt-PT/Esri.ArcGISRuntime.resources.dll", "lib/net472/ro/Esri.ArcGISRuntime.resources.dll", "lib/net472/ru/Esri.ArcGISRuntime.resources.dll", "lib/net472/sk/Esri.ArcGISRuntime.resources.dll", "lib/net472/sl/Esri.ArcGISRuntime.resources.dll", "lib/net472/sr/Esri.ArcGISRuntime.resources.dll", "lib/net472/sv/Esri.ArcGISRuntime.resources.dll", "lib/net472/th/Esri.ArcGISRuntime.resources.dll", "lib/net472/tr/Esri.ArcGISRuntime.resources.dll", "lib/net472/uk/Esri.ArcGISRuntime.resources.dll", "lib/net472/vi/Esri.ArcGISRuntime.resources.dll", "lib/net472/zh-CN/Esri.ArcGISRuntime.resources.dll", "lib/net472/zh-HK/Esri.ArcGISRuntime.resources.dll", "lib/net472/zh-<PERSON>/Esri.ArcGISRuntime.resources.dll", "lib/net472/zh-TW/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-android34.0/Esri.ArcGISRuntime.dll", "lib/net8.0-android34.0/Esri.ArcGISRuntime.xml", "lib/net8.0-android34.0/ar/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-android34.0/bg/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-android34.0/bs/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-android34.0/ca/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-android34.0/cs/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-android34.0/da/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-android34.0/de/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-android34.0/el/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-android34.0/es/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-android34.0/et/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-android34.0/fi/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-android34.0/fr/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-android34.0/he/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-android34.0/hr/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-android34.0/hu/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-android34.0/id/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-android34.0/it/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-android34.0/ja/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-android34.0/ko/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-android34.0/lt/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-android34.0/lv/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-android34.0/nb-NO/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-android34.0/nl/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-android34.0/no/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-android34.0/pl/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-android34.0/pt-BR/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-android34.0/pt-PT/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-android34.0/ro/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-android34.0/ru/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-android34.0/sk/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-android34.0/sl/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-android34.0/sr/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-android34.0/sv/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-android34.0/th/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-android34.0/tr/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-android34.0/uk/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-android34.0/vi/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-android34.0/zh-CN/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-android34.0/zh-HK/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-android34.0/zh-<PERSON>/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-android34.0/zh-TW/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-ios17.0/Esri.ArcGISRuntime.dll", "lib/net8.0-ios17.0/Esri.ArcGISRuntime.xml", "lib/net8.0-ios17.0/ar/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-ios17.0/bg/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-ios17.0/bs/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-ios17.0/ca/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-ios17.0/cs/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-ios17.0/da/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-ios17.0/de/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-ios17.0/el/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-ios17.0/es/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-ios17.0/et/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-ios17.0/fi/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-ios17.0/fr/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-ios17.0/he/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-ios17.0/hr/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-ios17.0/hu/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-ios17.0/id/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-ios17.0/it/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-ios17.0/ja/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-ios17.0/ko/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-ios17.0/lt/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-ios17.0/lv/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-ios17.0/nb-NO/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-ios17.0/nl/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-ios17.0/no/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-ios17.0/pl/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-ios17.0/pt-BR/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-ios17.0/pt-PT/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-ios17.0/ro/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-ios17.0/ru/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-ios17.0/sk/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-ios17.0/sl/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-ios17.0/sr/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-ios17.0/sv/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-ios17.0/th/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-ios17.0/tr/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-ios17.0/uk/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-ios17.0/vi/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-ios17.0/zh-CN/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-ios17.0/zh-HK/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-ios17.0/zh-<PERSON>/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-ios17.0/zh-TW/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-maccatalyst17.0/Esri.ArcGISRuntime.dll", "lib/net8.0-maccatalyst17.0/Esri.ArcGISRuntime.xml", "lib/net8.0-maccatalyst17.0/ar/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-maccatalyst17.0/bg/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-maccatalyst17.0/bs/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-maccatalyst17.0/ca/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-maccatalyst17.0/cs/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-maccatalyst17.0/da/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-maccatalyst17.0/de/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-maccatalyst17.0/el/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-maccatalyst17.0/es/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-maccatalyst17.0/et/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-maccatalyst17.0/fi/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-maccatalyst17.0/fr/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-maccatalyst17.0/he/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-maccatalyst17.0/hr/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-maccatalyst17.0/hu/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-maccatalyst17.0/id/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-maccatalyst17.0/it/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-maccatalyst17.0/ja/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-maccatalyst17.0/ko/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-maccatalyst17.0/lt/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-maccatalyst17.0/lv/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-maccatalyst17.0/nb-NO/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-maccatalyst17.0/nl/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-maccatalyst17.0/no/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-maccatalyst17.0/pl/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-maccatalyst17.0/pt-BR/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-maccatalyst17.0/pt-PT/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-maccatalyst17.0/ro/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-maccatalyst17.0/ru/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-maccatalyst17.0/sk/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-maccatalyst17.0/sl/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-maccatalyst17.0/sr/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-maccatalyst17.0/sv/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-maccatalyst17.0/th/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-maccatalyst17.0/tr/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-maccatalyst17.0/uk/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-maccatalyst17.0/vi/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-maccatalyst17.0/zh-CN/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-maccatalyst17.0/zh-HK/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-maccatalyst17.0/zh-<PERSON>/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-maccatalyst17.0/zh-TW/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-windows10.0.19041/Esri.ArcGISRuntime.dll", "lib/net8.0-windows10.0.19041/Esri.ArcGISRuntime.xml", "lib/net8.0-windows10.0.19041/ar/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-windows10.0.19041/bg/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-windows10.0.19041/bs/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-windows10.0.19041/ca/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-windows10.0.19041/cs/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-windows10.0.19041/da/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-windows10.0.19041/de/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-windows10.0.19041/el/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-windows10.0.19041/es/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-windows10.0.19041/et/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-windows10.0.19041/fi/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-windows10.0.19041/fr/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-windows10.0.19041/he/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-windows10.0.19041/hr/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-windows10.0.19041/hu/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-windows10.0.19041/id/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-windows10.0.19041/it/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-windows10.0.19041/ja/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-windows10.0.19041/ko/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-windows10.0.19041/lt/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-windows10.0.19041/lv/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-windows10.0.19041/nb-NO/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-windows10.0.19041/nl/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-windows10.0.19041/no/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-windows10.0.19041/pl/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-windows10.0.19041/pt-BR/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-windows10.0.19041/pt-PT/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-windows10.0.19041/ro/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-windows10.0.19041/ru/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-windows10.0.19041/sk/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-windows10.0.19041/sl/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-windows10.0.19041/sr/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-windows10.0.19041/sv/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-windows10.0.19041/th/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-windows10.0.19041/tr/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-windows10.0.19041/uk/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-windows10.0.19041/vi/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-windows10.0.19041/zh-CN/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-windows10.0.19041/zh-HK/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-windows10.0.19041/zh-<PERSON>/Esri.ArcGISRuntime.resources.dll", "lib/net8.0-windows10.0.19041/zh-TW/Esri.ArcGISRuntime.resources.dll", "lib/net8.0/Esri.ArcGISRuntime.dll", "lib/net8.0/Esri.ArcGISRuntime.xml", "lib/net8.0/ar/Esri.ArcGISRuntime.resources.dll", "lib/net8.0/bg/Esri.ArcGISRuntime.resources.dll", "lib/net8.0/bs/Esri.ArcGISRuntime.resources.dll", "lib/net8.0/ca/Esri.ArcGISRuntime.resources.dll", "lib/net8.0/cs/Esri.ArcGISRuntime.resources.dll", "lib/net8.0/da/Esri.ArcGISRuntime.resources.dll", "lib/net8.0/de/Esri.ArcGISRuntime.resources.dll", "lib/net8.0/el/Esri.ArcGISRuntime.resources.dll", "lib/net8.0/es/Esri.ArcGISRuntime.resources.dll", "lib/net8.0/et/Esri.ArcGISRuntime.resources.dll", "lib/net8.0/fi/Esri.ArcGISRuntime.resources.dll", "lib/net8.0/fr/Esri.ArcGISRuntime.resources.dll", "lib/net8.0/he/Esri.ArcGISRuntime.resources.dll", "lib/net8.0/hr/Esri.ArcGISRuntime.resources.dll", "lib/net8.0/hu/Esri.ArcGISRuntime.resources.dll", "lib/net8.0/id/Esri.ArcGISRuntime.resources.dll", "lib/net8.0/it/Esri.ArcGISRuntime.resources.dll", "lib/net8.0/ja/Esri.ArcGISRuntime.resources.dll", "lib/net8.0/ko/Esri.ArcGISRuntime.resources.dll", "lib/net8.0/lt/Esri.ArcGISRuntime.resources.dll", "lib/net8.0/lv/Esri.ArcGISRuntime.resources.dll", "lib/net8.0/nb-NO/Esri.ArcGISRuntime.resources.dll", "lib/net8.0/nl/Esri.ArcGISRuntime.resources.dll", "lib/net8.0/no/Esri.ArcGISRuntime.resources.dll", "lib/net8.0/pl/Esri.ArcGISRuntime.resources.dll", "lib/net8.0/pt-BR/Esri.ArcGISRuntime.resources.dll", "lib/net8.0/pt-PT/Esri.ArcGISRuntime.resources.dll", "lib/net8.0/ro/Esri.ArcGISRuntime.resources.dll", "lib/net8.0/ru/Esri.ArcGISRuntime.resources.dll", "lib/net8.0/sk/Esri.ArcGISRuntime.resources.dll", "lib/net8.0/sl/Esri.ArcGISRuntime.resources.dll", "lib/net8.0/sr/Esri.ArcGISRuntime.resources.dll", "lib/net8.0/sv/Esri.ArcGISRuntime.resources.dll", "lib/net8.0/th/Esri.ArcGISRuntime.resources.dll", "lib/net8.0/tr/Esri.ArcGISRuntime.resources.dll", "lib/net8.0/uk/Esri.ArcGISRuntime.resources.dll", "lib/net8.0/vi/Esri.ArcGISRuntime.resources.dll", "lib/net8.0/zh-CN/Esri.ArcGISRuntime.resources.dll", "lib/net8.0/zh-HK/Esri.ArcGISRuntime.resources.dll", "lib/net8.0/zh-Hans/Esri.ArcGISRuntime.resources.dll", "lib/net8.0/zh-TW/Esri.ArcGISRuntime.resources.dll", "lib/netstandard2.0/Esri.ArcGISRuntime.dll", "lib/netstandard2.0/Esri.ArcGISRuntime.xml", "lib/netstandard2.0/ar/Esri.ArcGISRuntime.resources.dll", "lib/netstandard2.0/bg/Esri.ArcGISRuntime.resources.dll", "lib/netstandard2.0/bs/Esri.ArcGISRuntime.resources.dll", "lib/netstandard2.0/ca/Esri.ArcGISRuntime.resources.dll", "lib/netstandard2.0/cs/Esri.ArcGISRuntime.resources.dll", "lib/netstandard2.0/da/Esri.ArcGISRuntime.resources.dll", "lib/netstandard2.0/de/Esri.ArcGISRuntime.resources.dll", "lib/netstandard2.0/el/Esri.ArcGISRuntime.resources.dll", "lib/netstandard2.0/es/Esri.ArcGISRuntime.resources.dll", "lib/netstandard2.0/et/Esri.ArcGISRuntime.resources.dll", "lib/netstandard2.0/fi/Esri.ArcGISRuntime.resources.dll", "lib/netstandard2.0/fr/Esri.ArcGISRuntime.resources.dll", "lib/netstandard2.0/he/Esri.ArcGISRuntime.resources.dll", "lib/netstandard2.0/hr/Esri.ArcGISRuntime.resources.dll", "lib/netstandard2.0/hu/Esri.ArcGISRuntime.resources.dll", "lib/netstandard2.0/id/Esri.ArcGISRuntime.resources.dll", "lib/netstandard2.0/it/Esri.ArcGISRuntime.resources.dll", "lib/netstandard2.0/ja/Esri.ArcGISRuntime.resources.dll", "lib/netstandard2.0/ko/Esri.ArcGISRuntime.resources.dll", "lib/netstandard2.0/lt/Esri.ArcGISRuntime.resources.dll", "lib/netstandard2.0/lv/Esri.ArcGISRuntime.resources.dll", "lib/netstandard2.0/nb-NO/Esri.ArcGISRuntime.resources.dll", "lib/netstandard2.0/nl/Esri.ArcGISRuntime.resources.dll", "lib/netstandard2.0/no/Esri.ArcGISRuntime.resources.dll", "lib/netstandard2.0/pl/Esri.ArcGISRuntime.resources.dll", "lib/netstandard2.0/pt-BR/Esri.ArcGISRuntime.resources.dll", "lib/netstandard2.0/pt-PT/Esri.ArcGISRuntime.resources.dll", "lib/netstandard2.0/ro/Esri.ArcGISRuntime.resources.dll", "lib/netstandard2.0/ru/Esri.ArcGISRuntime.resources.dll", "lib/netstandard2.0/sk/Esri.ArcGISRuntime.resources.dll", "lib/netstandard2.0/sl/Esri.ArcGISRuntime.resources.dll", "lib/netstandard2.0/sr/Esri.ArcGISRuntime.resources.dll", "lib/netstandard2.0/sv/Esri.ArcGISRuntime.resources.dll", "lib/netstandard2.0/th/Esri.ArcGISRuntime.resources.dll", "lib/netstandard2.0/tr/Esri.ArcGISRuntime.resources.dll", "lib/netstandard2.0/uk/Esri.ArcGISRuntime.resources.dll", "lib/netstandard2.0/vi/Esri.ArcGISRuntime.resources.dll", "lib/netstandard2.0/zh-CN/Esri.ArcGISRuntime.resources.dll", "lib/netstandard2.0/zh-HK/Esri.ArcGISRuntime.resources.dll", "lib/netstandard2.0/zh-<PERSON>/Esri.ArcGISRuntime.resources.dll", "lib/netstandard2.0/zh-TW/Esri.ArcGISRuntime.resources.dll", "lib/uap10.0.19041/Esri.ArcGISRuntime.dll", "lib/uap10.0.19041/Esri.ArcGISRuntime.pri", "lib/uap10.0.19041/Esri.ArcGISRuntime.xml", "resources/network_analyst/strings.bin"]}, "Esri.ArcGISRuntime.runtimes.win/200.6.0": {"sha512": "LKAyPN85+1sWBjHf/KPrsTpZMgsXKWSYXD+aM/ufr3L1xFAs54oQOe0aEkXWldlyyeOUQrAGbztdX69Veh8Unw==", "type": "package", "path": "esri.arcgisruntime.runtimes.win/200.6.0", "files": [".nupkg.metadata", ".signature.p7s", "build/net472/Esri.ArcGISRuntime.runtimes.win.targets", "build/netstandard1.0/_._", "buildTransitive/net472/Esri.ArcGISRuntime.runtimes.win.targets", "buildTransitive/netstandard1.0/_._", "esri.arcgisruntime.runtimes.win.200.6.0.nupkg.sha512", "esri.arcgisruntime.runtimes.win.nuspec", "icon.png", "legal/Copyright_and_Trademarks.pdf", "legal/eula.txt", "legal/open-source-acknowledgements.pdf", "lib/net472/_._", "lib/net8.0-windows10.0.19041.0/_._", "lib/uap10.0.19041/_._", "runtimes/win-arm64/native/RuntimeCoreNet200_6.dll", "runtimes/win-arm64/native/runtimecore.dll", "runtimes/win-arm64/native/runtimecoreAssembly.manifest", "runtimes/win-x64/native/RuntimeCoreNet200_6.dll", "runtimes/win-x64/native/runtimecore.dll", "runtimes/win-x64/native/runtimecoreAssembly.manifest", "runtimes/win-x86/native/RuntimeCoreNet200_6.dll", "runtimes/win-x86/native/runtimecore.dll", "runtimes/win-x86/native/runtimecoreAssembly.manifest"]}, "Microsoft.Web.WebView2/1.0.3296.44": {"sha512": "dDCbfdpka+X9TDpTqlJY1Ydz+BQ/KOYNtnJvXXqtBONe96rkwk9wZSLasw7yCo4NI1rXuDg2qAfDtV9K1vl8eg==", "type": "package", "path": "microsoft.web.webview2/1.0.3296.44", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "NOTICE.txt", "WebView2.idl", "WebView2.tlb", "build/Common.targets", "build/Microsoft.Web.WebView2.targets", "build/WebView2Rules.Project.xml", "build/native/Microsoft.Web.WebView2.targets", "build/native/arm64/WebView2Loader.dll", "build/native/arm64/WebView2Loader.dll.lib", "build/native/arm64/WebView2LoaderStatic.lib", "build/native/include-winrt/WebView2Interop.h", "build/native/include-winrt/WebView2Interop.idl", "build/native/include-winrt/WebView2Interop.tlb", "build/native/include/WebView2.h", "build/native/include/WebView2EnvironmentOptions.h", "build/native/x64/WebView2Loader.dll", "build/native/x64/WebView2Loader.dll.lib", "build/native/x64/WebView2LoaderStatic.lib", "build/native/x86/WebView2Loader.dll", "build/native/x86/WebView2Loader.dll.lib", "build/native/x86/WebView2LoaderStatic.lib", "build/wv2winrt.targets", "buildTransitive/Microsoft.Web.WebView2.targets", "lib/Microsoft.Web.WebView2.Core.winmd", "lib/net462/Microsoft.Web.WebView2.Core.dll", "lib/net462/Microsoft.Web.WebView2.Core.xml", "lib/net462/Microsoft.Web.WebView2.WinForms.dll", "lib/net462/Microsoft.Web.WebView2.WinForms.xml", "lib/net462/Microsoft.Web.WebView2.Wpf.dll", "lib/net462/Microsoft.Web.WebView2.Wpf.xml", "lib_manual/net5.0-windows10.0.17763.0/Microsoft.Web.WebView2.Wpf.dll", "lib_manual/net5.0-windows10.0.17763.0/Microsoft.Web.WebView2.Wpf.xml", "lib_manual/net6.0-windows10.0.17763.0/Microsoft.Web.WebView2.Core.Projection.dll", "lib_manual/net8.0-windows10.0.17763.0/Microsoft.Web.WebView2.Core.Projection.dll", "lib_manual/netcoreapp3.0/Microsoft.Web.WebView2.Core.dll", "lib_manual/netcoreapp3.0/Microsoft.Web.WebView2.Core.xml", "lib_manual/netcoreapp3.0/Microsoft.Web.WebView2.WinForms.dll", "lib_manual/netcoreapp3.0/Microsoft.Web.WebView2.WinForms.xml", "lib_manual/netcoreapp3.0/Microsoft.Web.WebView2.Wpf.dll", "lib_manual/netcoreapp3.0/Microsoft.Web.WebView2.Wpf.xml", "microsoft.web.webview2.1.0.3296.44.nupkg.sha512", "microsoft.web.webview2.nuspec", "runtimes/win-arm64/native/WebView2Loader.dll", "runtimes/win-arm64/native_uap/Microsoft.Web.WebView2.Core.dll", "runtimes/win-x64/native/WebView2Loader.dll", "runtimes/win-x64/native_uap/Microsoft.Web.WebView2.Core.dll", "runtimes/win-x86/native/WebView2Loader.dll", "runtimes/win-x86/native_uap/Microsoft.Web.WebView2.Core.dll", "tools/VisualStudioToolsManifest.xml", "tools/wv2winrt/Antlr3.Runtime.dll", "tools/wv2winrt/Antlr4.StringTemplate.dll", "tools/wv2winrt/System.Buffers.dll", "tools/wv2winrt/System.CommandLine.DragonFruit.dll", "tools/wv2winrt/System.CommandLine.Rendering.dll", "tools/wv2winrt/System.CommandLine.dll", "tools/wv2winrt/System.Memory.dll", "tools/wv2winrt/System.Numerics.Vectors.dll", "tools/wv2winrt/System.Runtime.CompilerServices.Unsafe.dll", "tools/wv2winrt/codegen_util.dll", "tools/wv2winrt/concrt140_app.dll", "tools/wv2winrt/cs/System.CommandLine.resources.dll", "tools/wv2winrt/de/System.CommandLine.resources.dll", "tools/wv2winrt/es/System.CommandLine.resources.dll", "tools/wv2winrt/fr/System.CommandLine.resources.dll", "tools/wv2winrt/it/System.CommandLine.resources.dll", "tools/wv2winrt/ja/System.CommandLine.resources.dll", "tools/wv2winrt/ko/System.CommandLine.resources.dll", "tools/wv2winrt/msvcp140_1_app.dll", "tools/wv2winrt/msvcp140_2_app.dll", "tools/wv2winrt/msvcp140_app.dll", "tools/wv2winrt/pl/System.CommandLine.resources.dll", "tools/wv2winrt/pt-BR/System.CommandLine.resources.dll", "tools/wv2winrt/ru/System.CommandLine.resources.dll", "tools/wv2winrt/tr/System.CommandLine.resources.dll", "tools/wv2winrt/type_hierarchy.dll", "tools/wv2winrt/vcamp140_app.dll", "tools/wv2winrt/vccorlib140_app.dll", "tools/wv2winrt/vcomp140_app.dll", "tools/wv2winrt/vcruntime140_app.dll", "tools/wv2winrt/winrt_winmd.dll", "tools/wv2winrt/winrt_winmd.winmd", "tools/wv2winrt/wv2winrt.exe", "tools/wv2winrt/wv2winrt.exe.config", "tools/wv2winrt/wv2winrt.xml", "tools/wv2winrt/zh-Hans/System.CommandLine.resources.dll", "tools/wv2winrt/zh-Hant/System.CommandLine.resources.dll"]}, "Newtonsoft.Json/13.0.3": {"sha512": "HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "type": "package", "path": "newtonsoft.json/13.0.3", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.md", "README.md", "lib/net20/Newtonsoft.Json.dll", "lib/net20/Newtonsoft.Json.xml", "lib/net35/Newtonsoft.Json.dll", "lib/net35/Newtonsoft.Json.xml", "lib/net40/Newtonsoft.Json.dll", "lib/net40/Newtonsoft.Json.xml", "lib/net45/Newtonsoft.Json.dll", "lib/net45/Newtonsoft.Json.xml", "lib/net6.0/Newtonsoft.Json.dll", "lib/net6.0/Newtonsoft.Json.xml", "lib/netstandard1.0/Newtonsoft.Json.dll", "lib/netstandard1.0/Newtonsoft.Json.xml", "lib/netstandard1.3/Newtonsoft.Json.dll", "lib/netstandard1.3/Newtonsoft.Json.xml", "lib/netstandard2.0/Newtonsoft.Json.dll", "lib/netstandard2.0/Newtonsoft.Json.xml", "newtonsoft.json.13.0.3.nupkg.sha512", "newtonsoft.json.nuspec", "packageIcon.png"]}, "System.Text.Encoding.CodePages/8.0.0": {"sha512": "OZIsVplFGaVY90G2SbpgU7EnCoOO5pw1t4ic21dBF3/1omrJFpAGoNAVpPyMVOC90/hvgkGG3VFqR13YgZMQfg==", "type": "package", "path": "system.text.encoding.codepages/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Text.Encoding.CodePages.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Text.Encoding.CodePages.targets", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net462/System.Text.Encoding.CodePages.dll", "lib/net462/System.Text.Encoding.CodePages.xml", "lib/net6.0/System.Text.Encoding.CodePages.dll", "lib/net6.0/System.Text.Encoding.CodePages.xml", "lib/net7.0/System.Text.Encoding.CodePages.dll", "lib/net7.0/System.Text.Encoding.CodePages.xml", "lib/net8.0/System.Text.Encoding.CodePages.dll", "lib/net8.0/System.Text.Encoding.CodePages.xml", "lib/netstandard2.0/System.Text.Encoding.CodePages.dll", "lib/netstandard2.0/System.Text.Encoding.CodePages.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "runtimes/win/lib/net6.0/System.Text.Encoding.CodePages.dll", "runtimes/win/lib/net6.0/System.Text.Encoding.CodePages.xml", "runtimes/win/lib/net7.0/System.Text.Encoding.CodePages.dll", "runtimes/win/lib/net7.0/System.Text.Encoding.CodePages.xml", "runtimes/win/lib/net8.0/System.Text.Encoding.CodePages.dll", "runtimes/win/lib/net8.0/System.Text.Encoding.CodePages.xml", "system.text.encoding.codepages.8.0.0.nupkg.sha512", "system.text.encoding.codepages.nuspec", "useSharedDesignerContext.txt"]}}, "projectFileDependencyGroups": {"net8.0-windows7.0": ["DuckDB.NET.Data.Full >= 1.2.0", "Esri.ArcGISRuntime >= 200.6.0", "Microsoft.Web.WebView2 >= 1.0.3296.44", "Newtonsoft.Json >= 13.0.3", "System.Text.Encoding.CodePages >= 8.0.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "D:\\RUANJIAN\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\History\\-6a74bfde\\zN4h.csproj", "projectName": "zN4h", "projectPath": "C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\History\\-6a74bfde\\zN4h.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\History\\-6a74bfde\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\RUANJIAN\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "dependencies": {"DuckDB.NET.Data.Full": {"target": "Package", "version": "[1.2.0, )"}, "Esri.ArcGISRuntime": {"target": "Package", "version": "[200.6.0, )"}, "Microsoft.Web.WebView2": {"target": "Package", "version": "[1.0.3296.44, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "System.Text.Encoding.CodePages": {"target": "Package", "version": "[8.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}, "runtimes": {"win-x64": {"#import": []}}}}