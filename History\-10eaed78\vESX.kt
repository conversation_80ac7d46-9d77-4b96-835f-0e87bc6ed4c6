package com.example.surveyassistant.map

import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.Rect
import android.util.Log
import kotlinx.coroutines.*
import org.gdal.gdal.Dataset
import org.gdal.gdal.gdal
import org.gdal.osr.CoordinateTransformation
import org.gdal.osr.SpatialReference
import org.osmdroid.util.BoundingBox
import org.osmdroid.util.GeoPoint
import org.osmdroid.views.MapView
import org.osmdroid.views.Projection
import org.osmdroid.views.overlay.Overlay
import com.example.surveyassistant.utils.CoordinateSystemDetector
import java.io.File
import kotlin.math.*

/**
 * 高性能GeoTIFF栅格图层显示覆盖层
 * 支持坐标系自动识别、动态投影转换和高效渲染
 */
class GeoTiffOverlay(
    private val mapView: MapView,
    private val file: File
) : Overlay() {
    
    companion object {
        private const val TAG = "GeoTiffOverlay"
        private const val MAX_BITMAP_SIZE = 2048
        private const val CACHE_ZOOM_TOLERANCE = 1.0
        
        /**
         * 检查文件是否可以打开
         */
        fun canOpen(file: File): Boolean {
            return try {
                val dataset = gdal.Open(file.absolutePath, 0)
                val result = dataset != null
                dataset?.delete()
                result
            } catch (e: Exception) {
                Log.e(TAG, "检查GeoTIFF文件时出错: ${e.message}", e)
                false
            }
        }
    }
    
    // GDAL数据集
    private var dataset: Dataset? = null
    
    // 地理范围（WGS84坐标系）
    private var boundingBox: BoundingBox? = null
    
    // 图像尺寸
    private var width: Int = 0
    private var height: Int = 0
    
    // 仿射变换参数
    private var geoTransform: DoubleArray? = null
    
    // 坐标转换器
    private var coordTransform: CoordinateTransformation? = null
    private var invCoordTransform: CoordinateTransformation? = null
    
    // 原始投影信息
    private var projectionWkt: String = ""
    private var sourceSRS: SpatialReference? = null
    private var targetSRS: SpatialReference? = null
    
    // 绘制参数
    private val paint = Paint().apply {
        isAntiAlias = true
        isFilterBitmap = true
    }
    
    // 优化的缓存和加载系统
    private val cache = GeoTiffCache.getInstance()
    private val asyncLoader = GeoTiffAsyncLoader.getInstance()
    private val pyramidManager = GeoTiffPyramidManager.getInstance()

    // 缓存管理
    private var cachedBitmap: Bitmap? = null
    private var cachedBoundingBox: BoundingBox? = null
    private var cachedZoomLevel: Double = 0.0
    private var currentCacheKey: String? = null

    // 透明度
    private var alpha: Int = 255

    // 异步加载控制
    private var loadingJob: Job? = null
    private var currentAsyncTaskId: String? = null
    private var isLoading = false
    private val coroutineScope = CoroutineScope(Dispatchers.IO + SupervisorJob())

    // 性能优化参数
    private var lastMapMoveTime: Long = 0
    private val REFRESH_DELAY = 150L
    private var refreshRequested = false
    
    init {
        initializeGeoTIFF()
    }
    
    /**
     * 初始化GeoTIFF
     */
    private fun initializeGeoTIFF() {
        coroutineScope.launch {
            try {
                Log.d(TAG, "开始初始化GeoTIFF: ${file.absolutePath}")
                
                // 打开数据集
                dataset = gdal.Open(file.absolutePath, 0)
                if (dataset == null) {
                    Log.e(TAG, "无法打开GeoTIFF文件: ${file.absolutePath}")
                    return@launch
                }
                
                // 获取基本信息
                width = dataset!!.rasterXSize
                height = dataset!!.rasterYSize
                geoTransform = dataset!!.GetGeoTransform()
                projectionWkt = dataset!!.GetProjection() ?: ""
                
                Log.d(TAG, "GeoTIFF基本信息: 尺寸=${width}x${height}")
                Log.d(TAG, "投影信息: $projectionWkt")
                
                // 设置坐标转换
                setupCoordinateTransformation()
                
                // 计算边界框
                calculateBoundingBox()
                
                // 在主线程更新UI
                withContext(Dispatchers.Main) {
                    mapView.invalidate()
                }
                
                Log.d(TAG, "GeoTIFF初始化成功: 边界=${boundingBox}")
                
            } catch (e: Exception) {
                Log.e(TAG, "初始化GeoTIFF失败", e)
            }
        }
    }
    
    /**
     * 设置坐标转换 - 参考mapkancezhushou的智能投影处理
     */
    private fun setupCoordinateTransformation() {
        try {
            // 创建源坐标系
            sourceSRS = SpatialReference()

            // 检查投影是否为CGCS2000坐标系
            val isCGCS2000 = projectionWkt.contains("CGCS2000") || projectionWkt.contains("China_2000")
            var zoneNumber = -1
            var is3DegZone = false
            var is6DegZone = false

            Log.d(TAG, "原始投影WKT: $projectionWkt")
            Log.d(TAG, "检测到CGCS2000: $isCGCS2000")

            // 处理CGCS2000坐标系
            if (isCGCS2000) {
                // 尝试提取带号
                val zoneRegex = "zone (\\d+)".toRegex()
                val matchResult = zoneRegex.find(projectionWkt)
                if (matchResult != null) {
                    zoneNumber = matchResult.groupValues[1].toInt()

                    // 判断是3度带还是6度带
                    is3DegZone = projectionWkt.contains("3-degree") || projectionWkt.contains("3度带")
                    is6DegZone = projectionWkt.contains("6-degree") || projectionWkt.contains("6度带")

                    if (!is3DegZone && !is6DegZone) {
                        // 尝试从中央经线判断
                        if (projectionWkt.contains("central_meridian")) {
                            val meridianRegex = "central_meridian\",([-0-9.]+)".toRegex()
                            val meridianMatch = meridianRegex.find(projectionWkt)
                            if (meridianMatch != null) {
                                val centralMeridian = meridianMatch.groupValues[1].toDouble()
                                // 判断是否为3度带的中央经线
                                is3DegZone = centralMeridian % 3 == 0.0 && centralMeridian % 6 != 0.0
                                // 判断是否为6度带的中央经线
                                is6DegZone = centralMeridian % 6 == 0.0
                            }
                        } else {
                            // 如果无法判断，默认按3度带处理
                            is3DegZone = true
                        }
                    }
                } else {
                    // 尝试从坐标值估计带号
                    val geoTransform = this.geoTransform
                    if (geoTransform != null && geoTransform[0] > 1.0e6) {
                        // 从x坐标估计带号
                        if (geoTransform[0] >= 1.0e7) {
                            // 可能是带号*1000万形式
                            zoneNumber = (geoTransform[0] / 1.0e7).toInt()
                        } else {
                            // 可能是带号*100万形式
                            zoneNumber = (geoTransform[0] / 1.0e6).toInt()
                        }

                        // 判断是3度带还是6度带
                        if (zoneNumber >= 1 && zoneNumber <= 60) {
                            // 中国区域带号范围约13-45
                            // 中国区域一般用3度带，其他区域可能用6度带
                            if (zoneNumber >= 13 && zoneNumber <= 45) {
                                is3DegZone = true
                            } else {
                                is6DegZone = true
                            }
                        }
                    }
                }

                Log.d(TAG, "CGCS2000参数: 带号=$zoneNumber, 3度带=$is3DegZone, 6度带=$is6DegZone")
            }

            // 对于CGCS2000坐标系，构建合适的WKT定义
            if (isCGCS2000 && zoneNumber > 0) {
                setupCGCS2000Projection(zoneNumber, is3DegZone)
            } else {
                setupStandardProjection()
            }

            // 创建目标坐标系(WGS84)
            targetSRS = SpatialReference()
            targetSRS!!.SetWellKnownGeogCS("WGS84")

            // 强制坐标系使用传统轴顺序 (经度,纬度)
            try {
                sourceSRS!!.SetAxisMappingStrategy(0) // OAMS_TRADITIONAL_GIS_ORDER
                targetSRS!!.SetAxisMappingStrategy(0) // OAMS_TRADITIONAL_GIS_ORDER
            } catch (e: Exception) {
                Log.w(TAG, "设置轴映射策略失败: ${e.message}")
            }

            // 创建坐标转换
            coordTransform = CoordinateTransformation(sourceSRS, targetSRS)
            invCoordTransform = CoordinateTransformation(targetSRS, sourceSRS)

            Log.d(TAG, "坐标转换设置成功")

        } catch (e: Exception) {
            Log.e(TAG, "设置坐标转换失败", e)
        }
    }

    /**
     * 设置CGCS2000投影坐标系
     */
    private fun setupCGCS2000Projection(zoneNumber: Int, is3DegZone: Boolean) {
        try {
            // 计算中央经线
            val centralMeridian = if (is3DegZone) {
                (zoneNumber * 3).toDouble()
            } else {
                ((zoneNumber * 6) - 3).toDouble()
            }

            // 构建WKT
            val wkt = if (is3DegZone) {
                """
                PROJCS["CGCS2000 / 3-degree Gauss-Kruger zone $zoneNumber",
                    GEOGCS["CGCS2000",
                        DATUM["China_2000",
                            SPHEROID["CGCS2000",6378137,298.257222101]],
                        PRIMEM["Greenwich",0],
                        UNIT["degree",0.0174532925199433]],
                    PROJECTION["Transverse_Mercator"],
                    PARAMETER["latitude_of_origin",0],
                    PARAMETER["central_meridian",$centralMeridian],
                    PARAMETER["scale_factor",1],
                    PARAMETER["false_easting",${zoneNumber}500000],
                    PARAMETER["false_northing",0],
                    UNIT["metre",1]]
                """.trimIndent()
            } else {
                """
                PROJCS["CGCS2000 / 6-degree Gauss-Kruger zone $zoneNumber",
                    GEOGCS["CGCS2000",
                        DATUM["China_2000",
                            SPHEROID["CGCS2000",6378137,298.257222101]],
                        PRIMEM["Greenwich",0],
                        UNIT["degree",0.0174532925199433]],
                    PROJECTION["Transverse_Mercator"],
                    PARAMETER["latitude_of_origin",0],
                    PARAMETER["central_meridian",$centralMeridian],
                    PARAMETER["scale_factor",1],
                    PARAMETER["false_easting",500000],
                    PARAMETER["false_northing",0],
                    UNIT["metre",1]]
                """.trimIndent()
            }

            val importResult = sourceSRS!!.ImportFromWkt(wkt)
            if (importResult != 0) {
                Log.e(TAG, "导入自定义CGCS2000 WKT失败: $importResult，尝试从原始投影导入")
                sourceSRS!!.ImportFromWkt(projectionWkt)
            } else {
                Log.d(TAG, "成功设置CGCS2000投影: 带号=$zoneNumber, 3度带=$is3DegZone, 中央经线=$centralMeridian")
            }
        } catch (e: Exception) {
            Log.e(TAG, "设置CGCS2000投影失败", e)
            // 回退到原始投影
            sourceSRS!!.ImportFromWkt(projectionWkt)
        }
    }

    /**
     * 设置标准投影坐标系
     */
    private fun setupStandardProjection() {
        try {
            if (projectionWkt.isNotEmpty()) {
                // 尝试直接从WKT导入
                val importResult = sourceSRS!!.ImportFromWkt(projectionWkt)

                if (importResult != 0) {
                    Log.e(TAG, "从WKT导入坐标系失败，尝试简化处理")
                    if (projectionWkt.contains("PROJCS") || projectionWkt.contains("GEOGCS")) {
                        // 尝试从简化的WKT导入
                        try {
                            val simplifiedWkt = simplifyWkt(projectionWkt)
                            sourceSRS!!.ImportFromWkt(simplifiedWkt)
                        } catch (e: Exception) {
                            Log.e(TAG, "处理简化WKT时出错: ${e.message}")
                            // 默认使用WGS84
                            sourceSRS!!.SetWellKnownGeogCS("WGS84")
                        }
                    }
                } else {
                    Log.d(TAG, "成功导入标准投影坐标系")
                }
            } else {
                // 默认使用WGS84
                Log.d(TAG, "未找到投影信息，默认使用WGS84")
                sourceSRS!!.SetWellKnownGeogCS("WGS84")
            }
        } catch (e: Exception) {
            Log.e(TAG, "设置标准投影失败", e)
            sourceSRS!!.SetWellKnownGeogCS("WGS84")
        }
    }

    /**
     * 简化WKT字符串，移除不必要的部分
     */
    private fun simplifyWkt(wkt: String): String {
        return try {
            // 提取WKT中的主要部分
            val regex = "(PROJCS|GEOGCS)\\[\"([^\"]+)\".*".toRegex()
            val matchResult = regex.find(wkt)

            if (matchResult != null) {
                val type = matchResult.groupValues[1]
                val name = matchResult.groupValues[2]

                if (type == "PROJCS") {
                    // 尝试从名称中提取关键信息
                    if (name.contains("WGS") && name.contains("UTM")) {
                        "PROJCS[\"WGS 84 / UTM\",GEOGCS[\"WGS 84\",DATUM[\"WGS_1984\",SPHEROID[\"WGS 84\",6378137,298.257223563]],PRIMEM[\"Greenwich\",0],UNIT[\"degree\",0.0174532925199433]],PROJECTION[\"Transverse_Mercator\"],PARAMETER[\"latitude_of_origin\",0],PARAMETER[\"central_meridian\",0],PARAMETER[\"scale_factor\",0.9996],PARAMETER[\"false_easting\",500000],PARAMETER[\"false_northing\",0],UNIT[\"metre\",1]]"
                    } else {
                        wkt
                    }
                } else {
                    // 对于地理坐标系，尝试简化
                    if (name.contains("WGS") && name.contains("84")) {
                        "GEOGCS[\"WGS 84\",DATUM[\"WGS_1984\",SPHEROID[\"WGS 84\",6378137,298.257223563]],PRIMEM[\"Greenwich\",0],UNIT[\"degree\",0.0174532925199433]]"
                    } else {
                        wkt
                    }
                }
            } else {
                wkt
            }
        } catch (e: Exception) {
            Log.e(TAG, "简化WKT时出错: ${e.message}")
            wkt
        }
    }

    /**
     * 应用坐标系设置（保留原有方法以兼容）
     */
    private fun applyCoordinateSystemSettings(coordInfo: CoordinateSystemDetector.CoordinateSystemInfo) {
        try {
            val settings = CoordinateSystemDetector.getRecommendedSettings(coordInfo)

            // 应用轴序设置
            if (coordInfo.needsAxisFix || settings["axisOrder"] == "traditional") {
                try {
                    sourceSRS?.SetAxisMappingStrategy(org.gdal.osr.osrConstants.OAMS_TRADITIONAL_GIS_ORDER)
                    Log.d(TAG, "应用传统GIS轴序")
                } catch (e: Exception) {
                    Log.w(TAG, "无法设置轴序策略: ${e.message}")
                }
            }

            // 根据坐标系类型应用特定优化
            when (coordInfo.type) {
                CoordinateSystemDetector.CoordinateSystemType.CGCS2000 -> {
                    applyCGCS2000Optimizations()
                }
                CoordinateSystemDetector.CoordinateSystemType.BEIJING54 -> {
                    applyBeijing54Optimizations()
                }
                CoordinateSystemDetector.CoordinateSystemType.XIAN80 -> {
                    applyXian80Optimizations()
                }
                CoordinateSystemDetector.CoordinateSystemType.UTM -> {
                    applyUTMOptimizations(coordInfo)
                }
                else -> {
                    Log.d(TAG, "使用默认坐标系设置")
                }
            }

        } catch (e: Exception) {
            Log.e(TAG, "应用坐标系设置失败", e)
        }
    }
    
    /**
     * 应用CGCS2000坐标系优化
     */
    private fun applyCGCS2000Optimizations() {
        try {
            Log.d(TAG, "应用CGCS2000坐标系优化")
            // CGCS2000与WGS84非常接近，转换精度很高
            // 可以考虑使用高精度转换参数
        } catch (e: Exception) {
            Log.e(TAG, "应用CGCS2000优化失败", e)
        }
    }

    /**
     * 应用北京54坐标系优化
     */
    private fun applyBeijing54Optimizations() {
        try {
            Log.d(TAG, "应用北京54坐标系优化")
            // 北京54坐标系可能需要特殊的转换参数
            // 可以设置更宽松的转换容差
        } catch (e: Exception) {
            Log.e(TAG, "应用北京54优化失败", e)
        }
    }

    /**
     * 应用西安80坐标系优化
     */
    private fun applyXian80Optimizations() {
        try {
            Log.d(TAG, "应用西安80坐标系优化")
            // 西安80坐标系优化
        } catch (e: Exception) {
            Log.e(TAG, "应用西安80优化失败", e)
        }
    }

    /**
     * 应用UTM坐标系优化
     */
    private fun applyUTMOptimizations(coordInfo: CoordinateSystemDetector.CoordinateSystemInfo) {
        try {
            Log.d(TAG, "应用UTM坐标系优化: 带号=${coordInfo.zone}")
            // UTM坐标系通常工作良好，可以使用高精度设置
        } catch (e: Exception) {
            Log.e(TAG, "应用UTM优化失败", e)
        }
    }
    
    /**
     * 记录坐标系信息
     */
    private fun logCoordinateSystemInfo(coordInfo: CoordinateSystemDetector.CoordinateSystemInfo) {
        try {
            val sourceSRS = this.sourceSRS ?: return

            Log.d(TAG, "=== 坐标系详细信息 ===")
            Log.d(TAG, "检测类型: ${coordInfo.type}")
            Log.d(TAG, "坐标系名称: ${coordInfo.name}")
            Log.d(TAG, "描述: ${coordInfo.description}")
            Log.d(TAG, "EPSG代码: ${coordInfo.epsgCode}")
            Log.d(TAG, "需要轴序修复: ${coordInfo.needsAxisFix}")
            coordInfo.zone?.let { Log.d(TAG, "投影带号: $it") }
            coordInfo.centralMeridian?.let { Log.d(TAG, "中央经线: $it") }
            Log.d(TAG, "投影名称: ${sourceSRS.GetAttrValue("PROJECTION")}")
            Log.d(TAG, "椭球体: ${sourceSRS.GetAttrValue("SPHEROID")}")
            Log.d(TAG, "基准面: ${sourceSRS.GetAttrValue("DATUM")}")

        } catch (e: Exception) {
            Log.e(TAG, "记录坐标系信息失败", e)
        }
    }
    
    /**
     * 计算边界框 - 参考mapkancezhushou的精确计算方法
     */
    private fun calculateBoundingBox() {
        try {
            val geoTransform = this.geoTransform ?: return
            val coordTransform = this.coordTransform ?: return

            // 计算四个角点的坐标
            val corners = arrayOf(
                doubleArrayOf(geoTransform[0], geoTransform[3]), // 左上角
                doubleArrayOf(geoTransform[0] + width * geoTransform[1], geoTransform[3] + width * geoTransform[4]), // 右上角
                doubleArrayOf(geoTransform[0] + height * geoTransform[2], geoTransform[3] + height * geoTransform[5]), // 左下角
                doubleArrayOf(geoTransform[0] + width * geoTransform[1] + height * geoTransform[2], geoTransform[3] + width * geoTransform[4] + height * geoTransform[5]) // 右下角
            )

            // 转换到WGS84
            var minLat = 90.0
            var maxLat = -90.0
            var minLon = 180.0
            var maxLon = -180.0

            var validPointCount = 0

            for (corner in corners) {
                val transformed = coordTransform.TransformPoint(corner[0], corner[1], 0.0)
                if (transformed != null && transformed.size == 3) {
                    val lon = transformed[0]
                    val lat = transformed[1]

                    // 检查结果是否合理 (跳过接近0,0的点)
                    if (abs(lon) > 0.0001 || abs(lat) > 0.0001) {
                        minLat = min(minLat, lat)
                        maxLat = max(maxLat, lat)
                        minLon = min(minLon, lon)
                        maxLon = max(maxLon, lon)
                        validPointCount++

                        Log.d(TAG, "角点转换: 原始(${corner[0]}, ${corner[1]}) -> WGS84($lat, $lon)")
                    }
                }
            }

            // 只有当至少有一个有效点时才创建边界框
            if (validPointCount > 0) {
                boundingBox = BoundingBox(maxLat, maxLon, minLat, minLon)
                Log.d(TAG, "边界框计算完成: $boundingBox (有效点数: $validPointCount)")
            } else {
                Log.e(TAG, "无法计算有效的边界框: 所有点都接近(0,0)或转换失败")
                boundingBox = null
            }

        } catch (e: Exception) {
            Log.e(TAG, "计算边界框失败", e)
            boundingBox = null
        }
    }

    /**
     * 设置透明度
     */
    fun setAlpha(alpha: Int) {
        this.alpha = alpha.coerceIn(0, 255)
        paint.alpha = this.alpha
        mapView.invalidate()
    }

    /**
     * 获取边界框
     */
    fun getBoundingBox(): BoundingBox? = boundingBox

    /**
     * 检查是否与当前视图相交
     */
    private fun intersectsWithView(projection: Projection): Boolean {
        val boundingBox = this.boundingBox ?: return false
        val viewBounds = projection.boundingBox
        return boundingBoxIntersects(boundingBox, viewBounds)
    }

    /**
     * 判断两个边界框是否相交
     */
    private fun boundingBoxIntersects(box1: BoundingBox, box2: BoundingBox): Boolean {
        return !(box1.lonEast < box2.lonWest || box1.lonWest > box2.lonEast ||
                box1.latNorth < box2.latSouth || box1.latSouth > box2.latNorth)
    }

    override fun draw(canvas: Canvas?, mapView: MapView?, shadow: Boolean) {
        if (shadow || canvas == null || mapView == null || dataset == null || boundingBox == null) {
            return
        }

        try {
            val projection = mapView.projection

            // 检查是否与当前视图相交
            if (!intersectsWithView(projection)) {
                return
            }

            // 检查是否需要重新生成位图
            val currentZoom = mapView.zoomLevelDouble
            val viewBounds = projection.boundingBox

            if (needsRefresh(viewBounds, currentZoom)) {
                generateBitmapAsync(projection, viewBounds, currentZoom)
            }

            // 绘制缓存的位图
            cachedBitmap?.let { bitmap ->
                if (!bitmap.isRecycled) {
                    drawBitmap(canvas, projection, bitmap)
                }
            }

        } catch (e: Exception) {
            Log.e(TAG, "绘制GeoTIFF时出错", e)
        }
    }

    /**
     * 检查是否需要刷新
     */
    private fun needsRefresh(viewBounds: BoundingBox, currentZoom: Double): Boolean {
        return cachedBitmap == null ||
               cachedBoundingBox == null ||
               !boundingBoxIntersects(viewBounds, cachedBoundingBox!!) ||
               abs(currentZoom - cachedZoomLevel) > CACHE_ZOOM_TOLERANCE
    }

    /**
     * 异步生成位图
     */
    private fun generateBitmapAsync(projection: Projection, viewBounds: BoundingBox, zoomLevel: Double) {
        // 取消之前的加载任务
        loadingJob?.cancel()

        loadingJob = coroutineScope.launch {
            try {
                val bitmap = generateBitmap(viewBounds, zoomLevel)

                if (bitmap != null && !bitmap.isRecycled) {
                    withContext(Dispatchers.Main) {
                        // 回收旧位图
                        cachedBitmap?.recycle()

                        // 缓存新位图
                        cachedBitmap = bitmap
                        cachedBoundingBox = viewBounds
                        cachedZoomLevel = zoomLevel

                        // 刷新地图
                        mapView.invalidate()
                    }
                }

            } catch (e: Exception) {
                if (e !is CancellationException) {
                    Log.e(TAG, "异步生成位图失败", e)
                }
            }
        }
    }

    /**
     * 生成位图
     */
    private suspend fun generateBitmap(viewBounds: BoundingBox, zoomLevel: Double): Bitmap? = withContext(Dispatchers.IO) {
        try {
            val dataset = <EMAIL> ?: return@withContext null
            val geoTransform = <EMAIL> ?: return@withContext null
            val boundingBox = <EMAIL> ?: return@withContext null

            // 计算相交区域
            val intersectBounds = BoundingBox(
                min(viewBounds.latNorth, boundingBox.latNorth),
                min(viewBounds.lonEast, boundingBox.lonEast),
                max(viewBounds.latSouth, boundingBox.latSouth),
                max(viewBounds.lonWest, boundingBox.lonWest)
            )

            // 转换地理坐标到像素坐标
            val pixelBounds = geoToPixel(intersectBounds, geoTransform)
            if (pixelBounds == null) {
                Log.w(TAG, "无法转换地理坐标到像素坐标")
                return@withContext null
            }

            // 计算读取区域
            val readX = max(0, pixelBounds.left)
            val readY = max(0, pixelBounds.top)
            val readWidth = min(width - readX, pixelBounds.width())
            val readHeight = min(height - readY, pixelBounds.height())

            if (readWidth <= 0 || readHeight <= 0) {
                Log.w(TAG, "读取区域无效: ${readWidth}x${readHeight}")
                return@withContext null
            }

            // 限制读取区域大小以防止内存问题
            val maxReadSize = 4096 // 最大读取尺寸
            val limitedReadWidth = min(readWidth, maxReadSize)
            val limitedReadHeight = min(readHeight, maxReadSize)

            if (limitedReadWidth != readWidth || limitedReadHeight != readHeight) {
                Log.d(TAG, "限制读取区域: 原始${readWidth}x${readHeight} -> 限制${limitedReadWidth}x${limitedReadHeight}")
            }

            // 计算输出位图尺寸（根据缩放级别调整）
            val scaleFactor = calculateScaleFactor(zoomLevel)
            val outputWidth = min(MAX_BITMAP_SIZE, (limitedReadWidth * scaleFactor).toInt())
            val outputHeight = min(MAX_BITMAP_SIZE, (limitedReadHeight * scaleFactor).toInt())

            // 确保输出尺寸合理
            if (outputWidth <= 0 || outputHeight <= 0) {
                Log.w(TAG, "输出尺寸无效: ${outputWidth}x${outputHeight}")
                return@withContext null
            }

            Log.d(TAG, "生成位图: 读取区域=${readX},${readY},${limitedReadWidth}x${limitedReadHeight}, 输出尺寸=${outputWidth}x${outputHeight}, 缩放=${scaleFactor}")

            // 读取栅格数据
            val bitmap = readRasterData(dataset, readX, readY, limitedReadWidth, limitedReadHeight, outputWidth, outputHeight)

            if (bitmap != null) {
                Log.d(TAG, "位图生成成功: ${bitmap.width}x${bitmap.height}")
            }

            bitmap

        } catch (e: Exception) {
            Log.e(TAG, "生成位图时出错", e)
            null
        }
    }

    /**
     * 计算缩放因子
     */
    private fun calculateScaleFactor(zoomLevel: Double): Double {
        // 根据缩放级别调整采样率
        return when {
            zoomLevel < 10 -> 0.25
            zoomLevel < 15 -> 0.5
            zoomLevel < 18 -> 0.75
            else -> 1.0
        }
    }

    /**
     * 地理坐标转像素坐标（支持动态投影）- 参考mapkancezhushou的精确转换
     */
    private fun geoToPixel(bounds: BoundingBox, geoTransform: DoubleArray): Rect? {
        try {
            val invCoordTransform = this.invCoordTransform ?: return null

            // 转换四个角点
            val corners = arrayOf(
                Pair(bounds.lonWest, bounds.latNorth), // 左上角
                Pair(bounds.lonEast, bounds.latNorth), // 右上角
                Pair(bounds.lonEast, bounds.latSouth), // 右下角
                Pair(bounds.lonWest, bounds.latSouth)  // 左下角
            )

            val pixelCorners = mutableListOf<Pair<Int, Int>>()

            for ((lon, lat) in corners) {
                // WGS84坐标转换到原始投影坐标系
                val projected = invCoordTransform.TransformPoint(lon, lat, 0.0)

                if (projected != null && projected.size == 3) {
                    val projX = projected[0]
                    val projY = projected[1]

                    // 投影坐标转像素坐标（使用仿射变换的逆变换）
                    val det = geoTransform[1] * geoTransform[5] - geoTransform[2] * geoTransform[4]

                    if (abs(det) < 1e-10) {
                        Log.w(TAG, "仿射变换矩阵奇异，使用简化版本")
                        // 使用简化版本
                        val pixelX = ((projX - geoTransform[0]) / geoTransform[1]).toInt()
                        val pixelY = ((projY - geoTransform[3]) / geoTransform[5]).toInt()
                        pixelCorners.add(Pair(pixelX, pixelY))
                    } else {
                        // 计算逆变换
                        val pixelX = ((geoTransform[5] * (projX - geoTransform[0]) -
                                      geoTransform[2] * (projY - geoTransform[3])) / det).toInt()
                        val pixelY = ((geoTransform[1] * (projY - geoTransform[3]) -
                                      geoTransform[4] * (projX - geoTransform[0])) / det).toInt()

                        pixelCorners.add(Pair(pixelX, pixelY))
                    }
                } else {
                    Log.w(TAG, "坐标转换失败: ($lon, $lat)")
                    return null
                }
            }

            if (pixelCorners.isEmpty()) {
                Log.w(TAG, "没有有效的像素坐标")
                return null
            }

            val minX = pixelCorners.minOf { it.first }
            val maxX = pixelCorners.maxOf { it.first }
            val minY = pixelCorners.minOf { it.second }
            val maxY = pixelCorners.maxOf { it.second }

            Log.d(TAG, "地理坐标转像素坐标: 边界($minX,$minY,$maxX,$maxY)")
            return Rect(minX, minY, maxX, maxY)

        } catch (e: Exception) {
            Log.e(TAG, "地理坐标转像素坐标失败", e)
            return null
        }
    }

    /**
     * 读取栅格数据 - 添加安全边界检查和内存管理
     */
    private fun readRasterData(
        dataset: Dataset,
        x: Int, y: Int, width: Int, height: Int,
        outputWidth: Int, outputHeight: Int
    ): Bitmap? {
        try {
            val bandCount = dataset.rasterCount
            if (bandCount < 1) {
                Log.w(TAG, "没有可用的波段")
                return null
            }

            // 获取数据集尺寸
            val datasetWidth = dataset.rasterXSize
            val datasetHeight = dataset.rasterYSize

            // 边界检查和修正
            val safeX = max(0, min(x, datasetWidth - 1))
            val safeY = max(0, min(y, datasetHeight - 1))
            val safeWidth = max(1, min(width, datasetWidth - safeX))
            val safeHeight = max(1, min(height, datasetHeight - safeY))

            // 限制输出尺寸以防止内存问题
            val maxOutputSize = 2048
            val safeOutputWidth = min(outputWidth, maxOutputSize)
            val safeOutputHeight = min(outputHeight, maxOutputSize)

            Log.d(TAG, "安全读取参数: 原始($x,$y,${width}x$height) -> 安全($safeX,$safeY,${safeWidth}x$safeHeight), 输出: ${safeOutputWidth}x$safeOutputHeight")

            // 检查读取区域是否过大
            val totalPixels = safeWidth.toLong() * safeHeight.toLong()
            val outputPixels = safeOutputWidth.toLong() * safeOutputHeight.toLong()

            if (totalPixels > 50_000_000L || outputPixels > 4_194_304L) { // 50M像素或4M输出像素
                Log.w(TAG, "读取区域过大，跳过: 输入=${totalPixels}像素, 输出=${outputPixels}像素")
                return null
            }

            // 创建位图
            val bitmap = Bitmap.createBitmap(safeOutputWidth, safeOutputHeight, Bitmap.Config.ARGB_8888)
            val pixels = IntArray(safeOutputWidth * safeOutputHeight)

            // 读取波段数据
            val bands = min(bandCount, 3) // 最多读取RGB三个波段
            val bandData = Array(bands) { ByteArray(safeOutputWidth * safeOutputHeight) }

            for (i in 0 until bands) {
                try {
                    val band = dataset.GetRasterBand(i + 1)
                    if (band == null) {
                        Log.w(TAG, "无法获取波段 ${i + 1}")
                        continue
                    }

                    // 使用GDAL的重采样读取，添加更多错误检查
                    val result = band.ReadRaster(
                        safeX, safeY, safeWidth, safeHeight,
                        safeOutputWidth, safeOutputHeight,
                        org.gdal.gdalconst.gdalconstConstants.GDT_Byte,
                        bandData[i]
                    )

                    if (result != org.gdal.gdalconst.gdalconstConstants.CE_None) {
                        Log.w(TAG, "读取波段 ${i + 1} 失败: $result")
                        // 如果读取失败，用0填充
                        bandData[i].fill(0)
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "读取波段 ${i + 1} 时发生异常", e)
                    // 如果发生异常，用0填充
                    bandData[i].fill(0)
                }
            }

            // 转换为ARGB像素
            for (i in pixels.indices) {
                val r = if (bands > 0) bandData[0][i].toInt() and 0xFF else 0
                val g = if (bands > 1) bandData[1][i].toInt() and 0xFF else r
                val b = if (bands > 2) bandData[2][i].toInt() and 0xFF else r
                val a = alpha

                pixels[i] = (a shl 24) or (r shl 16) or (g shl 8) or b
            }

            bitmap.setPixels(pixels, 0, safeOutputWidth, 0, 0, safeOutputWidth, safeOutputHeight)
            return bitmap

        } catch (e: Exception) {
            Log.e(TAG, "读取栅格数据失败", e)
            return null
        }
    }

    /**
     * 绘制位图
     */
    private fun drawBitmap(canvas: Canvas, projection: Projection, bitmap: Bitmap) {
        try {
            val boundingBox = this.boundingBox ?: return

            // 将地理坐标转换为屏幕坐标
            val topLeft = projection.toPixels(GeoPoint(boundingBox.latNorth, boundingBox.lonWest), null)
            val bottomRight = projection.toPixels(GeoPoint(boundingBox.latSouth, boundingBox.lonEast), null)

            val destRect = Rect(
                topLeft.x,
                topLeft.y,
                bottomRight.x,
                bottomRight.y
            )

            // 绘制位图
            canvas.drawBitmap(bitmap, null, destRect, paint)

        } catch (e: Exception) {
            Log.e(TAG, "绘制位图时出错", e)
        }
    }

    /**
     * 清理资源
     */
    fun cleanup() {
        try {
            // 取消异步任务
            loadingJob?.cancel()
            coroutineScope.cancel()

            // 清理位图
            cachedBitmap?.recycle()
            cachedBitmap = null

            // 清理GDAL资源
            dataset?.delete()
            dataset = null
            coordTransform?.delete()
            coordTransform = null
            invCoordTransform?.delete()
            invCoordTransform = null
            sourceSRS?.delete()
            sourceSRS = null
            targetSRS?.delete()
            targetSRS = null

            Log.d(TAG, "GeoTIFF资源已清理")
        } catch (e: Exception) {
            Log.e(TAG, "清理资源时出错", e)
        }
    }
}
