{"version": 2, "dgSpecHash": "LDfRLZnufbI=", "success": true, "projectFilePath": "C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\History\\1ff5506d\\fHYp.csproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\aspose.cad\\23.7.0\\aspose.cad.23.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\netdxf\\3.0.1\\netdxf.3.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.compilerservices.unsafe\\6.0.0\\system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.encoding.codepages\\6.0.0\\system.text.encoding.codepages.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.encodings.web\\6.0.0\\system.text.encodings.web.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.json\\6.0.0\\system.text.json.6.0.0.nupkg.sha512"], "logs": [{"code": "NU1603", "level": "Warning", "message": "fHYp 依赖于 netDxf (>= 3.0.0)，但没有找到 netDxf 3.0.0。已改为解析 netDxf 3.0.1。", "projectPath": "C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\History\\1ff5506d\\fHYp.csproj", "warningLevel": 1, "filePath": "C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\History\\1ff5506d\\fHYp.csproj", "libraryId": "netDxf", "targetGraphs": ["net8.0"]}]}