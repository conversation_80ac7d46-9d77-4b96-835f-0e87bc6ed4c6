#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Windsurf设备重置验证脚本
验证所有设备标识符是否已成功重置
"""

import os
import json
import re
from pathlib import Path

def verify_windsurf_reset():
    """验证Windsurf设备重置是否成功"""
    print("🔍 验证Windsurf设备重置状态...")
    print("=" * 50)
    
    windsurf_path = Path("d:/RUANJIAN/Windsurf")
    user_profile = Path(os.environ.get('USERPROFILE', ''))
    
    success_count = 0
    total_checks = 0
    
    # 1. 检查设备ID生成文件
    print("\n📝 1. 检查设备ID生成文件:")
    
    device_files = [
        windsurf_path / "resources/app/node_modules/@vscode/deviceid/dist/devdeviceid.js",
        windsurf_path / "resources/app/node_modules/@vscode/deviceid/dist/storage.js"
    ]
    
    for file_path in device_files:
        total_checks += 1
        if file_path.exists():
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                if "PATCHED" in content and "f20a703d-4462-411d-80ce-370eb7067285" in content:
                    print(f"   ✅ {file_path.name} - 已正确修改")
                    success_count += 1
                else:
                    print(f"   ❌ {file_path.name} - 修改不正确或未修改")
        else:
            print(f"   ❓ {file_path.name} - 文件不存在")
    
    # 2. 检查用户配置
    print("\n🗂️ 2. 检查用户配置:")
    
    total_checks += 1
    windsurf_config = user_profile / ".windsurf"
    if windsurf_config.exists():
        argv_file = windsurf_config / "argv.json"
        if argv_file.exists():
            try:
                with open(argv_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    if "29a6499c-5d46-4d99-ba49-41e1b0e76527" in content:
                        print(f"   ✅ crash-reporter-id 已更新")
                        success_count += 1
                    else:
                        print(f"   ❌ crash-reporter-id 未更新")
            except Exception as e:
                print(f"   ❌ 读取argv.json失败: {e}")
        else:
            print(f"   ❓ argv.json 不存在")
    else:
        print(f"   ❓ Windsurf配置目录不存在")
    
    # 3. 检查原生模块
    print("\n🔧 3. 检查原生模块:")
    
    total_checks += 1
    windows_node = windsurf_path / "resources/app/node_modules/@vscode/deviceid/build/Release/windows.node"
    if windows_node.exists():
        file_size = windows_node.stat().st_size
        if file_size <= 1024:  # 我们创建的占位文件
            print(f"   ✅ windows.node 已禁用 (文件大小: {file_size} 字节)")
            success_count += 1
        else:
            print(f"   ❌ windows.node 未禁用 (文件大小: {file_size} 字节)")
    else:
        print(f"   ❓ windows.node 不存在")
    
    # 4. 显示当前设备标识符
    print("\n🆔 4. 当前设备标识符:")
    print(f"   📱 新设备ID: 315a39a1-9bf1-4238-9b4a-44337a1aff4c")
    print(f"   💥 新崩溃报告ID: d167c7c0-a3bb-488d-b0e2-3e23b1bdccbb")
    print(f"   💻 新机器ID: bdb6adca69561044f97416e7ea149490f990089e1ff1996a13913be05c32062f7")
    print(f"   📊 新SQM ID: {{B5E8F593-5742-4C93-A776-793F9E6927EF}}")
    
    # 5. 总结
    print("\n" + "=" * 50)
    print(f"📊 验证结果: {success_count}/{total_checks} 项检查通过")
    
    if success_count == total_checks:
        print("🎉 所有检查通过！设备重置成功！")
        print("\n⚠️ 下一步操作:")
        print("   1. 完全关闭Windsurf")
        print("   2. 重启计算机")
        print("   3. 重新启动Windsurf")
        print("   4. 测试账号切换是否被识别为新设备")
    else:
        print("⚠️ 部分检查未通过，可能需要重新运行重置脚本")
    
    return success_count == total_checks

if __name__ == "__main__":
    try:
        verify_windsurf_reset()
    except Exception as e:
        print(f"❌ 验证脚本执行失败: {e}")
