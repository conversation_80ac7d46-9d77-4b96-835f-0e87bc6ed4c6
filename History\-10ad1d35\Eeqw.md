# SHP/TXT转KML工具 - 使用说明

## 快速开始

### 方法一：直接运行（推荐）
1. 双击 `启动程序.bat` 文件
2. 等待程序启动完成

### 方法二：命令行运行
1. 打开命令提示符
2. 切换到程序目录
3. 执行 `dotnet run`

### 方法三：发布为可执行文件
1. 双击 `发布程序.bat` 文件
2. 等待发布完成
3. 在 `publish` 文件夹中找到 `shptxttokml.exe`
4. 双击运行可执行文件

## 界面介绍

程序采用现代化的Material Design设计，界面简洁美观：

### 主要区域
1. **标题栏**: 显示程序名称和版本信息
2. **文件选择区**: 选择输入和输出文件
3. **文件信息区**: 显示选中文件的详细信息
4. **转换操作区**: 执行转换操作
5. **转换历史区**: 显示转换记录
6. **状态栏**: 显示当前操作状态

## 详细操作步骤

### 1. 选择输入文件
- 点击"浏览"按钮
- 在文件对话框中选择要转换的SHP文件
- 支持的格式：`.shp` (Shapefile)

### 2. 查看文件信息
选择文件后，程序会自动显示：
- **文件名**: 选中文件的名称
- **文件大小**: 文件大小（自动格式化为KB/MB/GB）
- **坐标系**: 自动识别的坐标系信息
- **要素数量**: SHP文件中包含的地理要素数量
- **修改时间**: 文件最后修改时间

### 3. 设置输出路径
- 点击"另存为"按钮
- 选择KML文件的保存位置和文件名
- 程序会自动建议输出文件名

### 4. 执行转换
- 点击"开始转换"按钮
- 等待转换完成（会显示进度条）
- 查看转换结果

### 5. 查看转换结果
转换完成后会显示：
- **成功/失败状态**: 转换是否成功
- **转换消息**: 详细的结果信息
- **要素数量**: 成功转换的要素数量
- **耗时**: 转换所用时间
- **输出文件路径**: 生成的KML文件位置

## 支持的坐标系

程序能够自动识别以下坐标系：

### 地理坐标系
- WGS84 (EPSG:4326)
- CGCS2000 (EPSG:4490)
- Beijing 1954

### 投影坐标系
- CGCS2000 3度分带投影
- Beijing 1954 3度分带投影
- 其他常用投影坐标系

**注意**: 所有输出的KML文件都会转换为WGS84坐标系，确保在Google Earth等软件中正确显示。

## 文件要求

### Shapefile要求
Shapefile是一种复合文件格式，需要包含以下文件：
- **必需文件**:
  - `.shp` - 主文件（几何图形）
  - `.shx` - 索引文件
  - `.dbf` - 属性数据文件
- **可选文件**:
  - `.prj` - 投影信息文件（强烈建议包含）
  - `.cpg` - 编码信息文件
  - `.sbn/.sbx` - 空间索引文件

### 文件命名
确保所有相关文件具有相同的基础文件名，例如：
```
数据.shp
数据.shx
数据.dbf
数据.prj
```

## 常见问题

### Q: 程序无法启动
**A**: 检查以下项目：
1. 确保已安装.NET 8.0 Runtime
2. 确保`key.txt`许可证文件存在
3. 检查防病毒软件是否阻止了程序运行

### Q: 转换失败
**A**: 可能的原因：
1. SHP文件不完整（缺少.shx或.dbf文件）
2. 文件损坏或格式不正确
3. 坐标系信息缺失或不支持
4. 输出路径没有写入权限

**注意**: 程序已优化错误处理策略：
- 对于有问题的数据字段，会自动跳过
- 对于无法处理的要素，会跳过并继续处理其他要素
- 所有属性字段都会转换为字符串类型，避免数据类型冲突
- 转换结果会显示成功转换的要素数量和跳过的要素数量

### Q: 坐标系识别错误
**A**: 解决方法：
1. 确保SHP文件包含.prj文件
2. 检查.prj文件内容是否正确
3. 手动确认数据的实际坐标系

### Q: 转换后的KML文件位置不正确
**A**: 这通常是坐标系转换问题：
1. 检查源数据的坐标系是否正确识别
2. 确认源数据的坐标范围是否合理
3. 在Google Earth中检查显示效果

## 性能优化建议

1. **大文件处理**: 对于包含大量要素的文件，转换可能需要较长时间，请耐心等待
2. **内存使用**: 处理大文件时确保系统有足够的可用内存
3. **磁盘空间**: 确保输出位置有足够的磁盘空间

## 技术支持

如遇到问题，请提供以下信息：
1. 错误信息截图
2. 输入文件的基本信息（文件大小、坐标系等）
3. 操作系统版本
4. .NET Runtime版本

---

**提示**: 建议在正式使用前，先用小的测试文件验证转换效果。
