package com.example.babylog.ui.screens

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.navigation.NavController
import com.example.babylog.data.database.BabyLogDatabase
import com.example.babylog.data.entity.Baby
import com.example.babylog.data.entity.FeedingRecord
import com.example.babylog.data.repository.BabyRepository
import com.example.babylog.navigation.Screen
import java.text.SimpleDateFormat
import java.util.*

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun FeedingScreen(navController: NavController) {
    val context = LocalContext.current
    val database = BabyLogDatabase.getDatabase(context)
    val repository = BabyRepository(
        database.babyDao(),
        database.healthRecordDao(),
        database.milestoneDao(),
        database.feedingRecordDao(),
        database.photoDao()
    )
    
    val babies by repository.getAllBabies().collectAsState(initial = emptyList())
    var selectedBaby by remember { mutableStateOf<Baby?>(null) }
    var selectedTab by remember { mutableStateOf(0) }
    
    // Set first baby as selected if available
    LaunchedEffect(babies) {
        if (babies.isNotEmpty() && selectedBaby == null) {
            selectedBaby = babies.first()
        }
    }
    
    val feedingRecords by remember(selectedBaby) {
        if (selectedBaby != null) {
            repository.getFeedingRecordsByBaby(selectedBaby!!.id)
        } else {
            kotlinx.coroutines.flow.flowOf(emptyList<FeedingRecord>())
        }
    }.collectAsState(initial = emptyList())
    
    val tabs = listOf("全部", "母乳喂养", "奶瓶喂养", "辅食")
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        // Header
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "喂养记录",
                style = MaterialTheme.typography.headlineMedium,
                fontWeight = FontWeight.Bold
            )
            
            selectedBaby?.let { baby ->
                IconButton(
                    onClick = { 
                        navController.navigate("${Screen.AddFeeding.route}?babyId=${baby.id}")
                    }
                ) {
                    Icon(Icons.Default.Add, contentDescription = "添加记录")
                }
            }
        }
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // Baby selector
        if (babies.isNotEmpty()) {
            var expanded by remember { mutableStateOf(false) }
            
            ExposedDropdownMenuBox(
                expanded = expanded,
                onExpandedChange = { expanded = !expanded }
            ) {
                OutlinedTextField(
                    value = selectedBaby?.name ?: "",
                    onValueChange = { },
                    readOnly = true,
                    label = { Text("选择宝宝") },
                    trailingIcon = {
                        ExposedDropdownMenuDefaults.TrailingIcon(expanded = expanded)
                    },
                    modifier = Modifier
                        .fillMaxWidth()
                        .menuAnchor()
                )
                
                ExposedDropdownMenu(
                    expanded = expanded,
                    onDismissRequest = { expanded = false }
                ) {
                    babies.forEach { baby ->
                        DropdownMenuItem(
                            text = { Text(baby.name) },
                            onClick = {
                                selectedBaby = baby
                                expanded = false
                            }
                        )
                    }
                }
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // Quick stats
            selectedBaby?.let { baby ->
                FeedingStatsCard(
                    feedingRecords = feedingRecords,
                    baby = baby
                )
                
                Spacer(modifier = Modifier.height(16.dp))
            }
            
            // Tabs
            ScrollableTabRow(selectedTabIndex = selectedTab) {
                tabs.forEachIndexed { index, title ->
                    Tab(
                        selected = selectedTab == index,
                        onClick = { selectedTab = index },
                        text = { Text(title) }
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // Feeding records list
            val filteredRecords = when (selectedTab) {
                0 -> feedingRecords
                1 -> feedingRecords.filter { record -> record.type == "breastfeeding" }
                2 -> feedingRecords.filter { record -> record.type == "bottle" }
                3 -> feedingRecords.filter { record -> record.type == "solid_food" }
                else -> feedingRecords
            }
            
            if (filteredRecords.isEmpty()) {
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    onClick = { 
                        selectedBaby?.let { baby ->
                            navController.navigate("${Screen.AddFeeding.route}?babyId=${baby.id}")
                        }
                    }
                ) {
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(24.dp),
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Icon(
                            Icons.Default.Restaurant,
                            contentDescription = null,
                            modifier = Modifier.size(48.dp),
                            tint = MaterialTheme.colorScheme.primary
                        )
                        Spacer(modifier = Modifier.height(16.dp))
                        Text(
                            text = "还没有喂养记录",
                            style = MaterialTheme.typography.titleMedium
                        )
                        Text(
                            text = "点击添加第一条记录",
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                }
            } else {
                LazyColumn(
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    items(filteredRecords.sortedByDescending { record -> record.startTime }) { record ->
                        FeedingRecordCard(record = record)
                    }
                }
            }
        } else {
            // No babies
            Card(
                modifier = Modifier.fillMaxWidth(),
                onClick = { navController.navigate("${Screen.AddBaby.route}?babyId=") }
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(24.dp),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Icon(
                        Icons.Default.PersonAdd,
                        contentDescription = null,
                        modifier = Modifier.size(48.dp),
                        tint = MaterialTheme.colorScheme.primary
                    )
                    Spacer(modifier = Modifier.height(16.dp))
                    Text(
                        text = "请先添加宝宝档案",
                        style = MaterialTheme.typography.titleMedium
                    )
                    Text(
                        text = "点击创建第一个宝宝档案",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        }
    }
}

@Composable
fun FeedingStatsCard(
    feedingRecords: List<FeedingRecord>,
    baby: Baby
) {
    val today = Calendar.getInstance().apply {
        set(Calendar.HOUR_OF_DAY, 0)
        set(Calendar.MINUTE, 0)
        set(Calendar.SECOND, 0)
        set(Calendar.MILLISECOND, 0)
    }.time
    
    val todayRecords = feedingRecords.filter { record -> record.startTime >= today }
    val lastFeeding = feedingRecords.maxByOrNull { record -> record.startTime }
    
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.primaryContainer
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Text(
                text = "今日统计",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.onPrimaryContainer
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                Column(horizontalAlignment = Alignment.CenterHorizontally) {
                    Text(
                        text = "${todayRecords.size}",
                        style = MaterialTheme.typography.headlineSmall,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.onPrimaryContainer
                    )
                    Text(
                        text = "次数",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onPrimaryContainer
                    )
                }
                
                Column(horizontalAlignment = Alignment.CenterHorizontally) {
                    val totalAmount = todayRecords.mapNotNull { it.amount }.sum()
                    Text(
                        text = if (totalAmount > 0) "${totalAmount.toInt()}ml" else "-",
                        style = MaterialTheme.typography.headlineSmall,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.onPrimaryContainer
                    )
                    Text(
                        text = "奶量",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onPrimaryContainer
                    )
                }
                
                Column(horizontalAlignment = Alignment.CenterHorizontally) {
                    val lastFeedingText = lastFeeding?.let { feeding ->
                        val now = Date()
                        val diffInMinutes = ((now.time - feeding.startTime.time) / (1000 * 60)).toInt()
                        when {
                            diffInMinutes < 60 -> "${diffInMinutes}分钟前"
                            diffInMinutes < 1440 -> "${diffInMinutes / 60}小时前"
                            else -> "${diffInMinutes / 1440}天前"
                        }
                    } ?: "-"
                    
                    Text(
                        text = lastFeedingText,
                        style = MaterialTheme.typography.headlineSmall,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.onPrimaryContainer
                    )
                    Text(
                        text = "上次喂养",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onPrimaryContainer
                    )
                }
            }
        }
    }
}

@Composable
fun FeedingRecordCard(record: FeedingRecord) {
    val timeFormat = SimpleDateFormat("HH:mm", Locale.getDefault())
    val dateFormat = SimpleDateFormat("MM月dd日", Locale.getDefault())
    
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.Top
            ) {
                Column(modifier = Modifier.weight(1f)) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            when (record.type) {
                                "breastfeeding" -> Icons.Default.ChildCare
                                "bottle" -> Icons.Default.LocalDrink
                                "solid_food" -> Icons.Default.Restaurant
                                else -> Icons.Default.Restaurant
                            },
                            contentDescription = null,
                            modifier = Modifier.size(20.dp),
                            tint = MaterialTheme.colorScheme.primary
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(
                            text = when (record.type) {
                                "breastfeeding" -> "母乳喂养"
                                "bottle" -> "奶瓶喂养"
                                "solid_food" -> "辅食"
                                else -> "喂养"
                            },
                            style = MaterialTheme.typography.titleMedium,
                            fontWeight = FontWeight.Medium
                        )
                    }
                    
                    Spacer(modifier = Modifier.height(4.dp))
                    
                    when (record.type) {
                        "breastfeeding" -> {
                            record.duration?.let { duration ->
                                Text(
                                    text = "${duration}分钟",
                                    style = MaterialTheme.typography.bodyLarge,
                                    fontWeight = FontWeight.Bold,
                                    color = MaterialTheme.colorScheme.primary
                                )
                            }
                        }
                        "bottle" -> {
                            record.amount?.let { amount ->
                                Text(
                                    text = "${amount.toInt()}ml",
                                    style = MaterialTheme.typography.bodyLarge,
                                    fontWeight = FontWeight.Bold,
                                    color = MaterialTheme.colorScheme.primary
                                )
                            }
                        }
                        "solid_food" -> {
                            record.foodName?.let { food ->
                                Text(
                                    text = food,
                                    style = MaterialTheme.typography.bodyLarge,
                                    fontWeight = FontWeight.Bold,
                                    color = MaterialTheme.colorScheme.primary
                                )
                            }
                            record.amount?.let { amount ->
                                Text(
                                    text = "${amount.toInt()}g",
                                    style = MaterialTheme.typography.bodyMedium,
                                    color = MaterialTheme.colorScheme.onSurfaceVariant
                                )
                            }
                        }
                    }
                    
                    record.notes?.let { notes ->
                        if (notes.isNotBlank()) {
                            Spacer(modifier = Modifier.height(4.dp))
                            Text(
                                text = notes,
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                    }
                }
                
                Column(
                    horizontalAlignment = Alignment.End
                ) {
                    Text(
                        text = dateFormat.format(record.startTime),
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    Text(
                        text = timeFormat.format(record.startTime),
                        style = MaterialTheme.typography.bodyMedium,
                        fontWeight = FontWeight.Medium
                    )
                    
                    record.endTime?.let { endTime ->
                        Text(
                            text = "- ${timeFormat.format(endTime)}",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                }
            }
        }
    }
}
