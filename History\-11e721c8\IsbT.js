/**
 * XIAOFU GIS助手 - 聊天界面JavaScript
 */

class ChatInterface {
    constructor() {
        this.messageCount = 0;
        this.isTyping = false;
        this.currentStreamingMessage = null;
        this.streamingContent = '';
        this.pendingCodeBlocks = new Map();
        this.lastFormattedContent = '';
        this.lastStreamingContent = '';
        this.isGenerating = false;

        // DOM元素
        this.messagesContainer = null;
        this.messageInput = null;
        this.sendButton = null;

        // 模块系统
        this.moduleSystem = window.moduleSystem;
        this.charCount = null;
        this.statusText = null;
        this.statusIndicator = null;
        this.toolIndicator = null;
        this.typingIndicator = null;
        this.historyBar = null;
        this.dropdownMenu = null;
        this.modeSwitch = null;
        this.chatLabel = null;
        this.agentLabel = null;
        this.currentMode = 'Chat'; // 默认为Chat模式
        this.isGenerating = false;
        this.streamingContent = '';
        this.currentStreamingMessage = null;
        this.currentConversationId = null;
        this.conversationHistory = [];
        this.currentToolBlockId = null;

        // 初始化
        this.init();
    }

    /**
     * 初始化聊天界面
     */
    init() {
        // 获取DOM元素
        this.messagesContainer = document.getElementById('messagesContainer');
        this.messageInput = document.getElementById('messageInput');
        this.sendButton = document.getElementById('sendButton');
        this.statusText = document.getElementById('statusText');
        this.statusIndicator = document.getElementById('statusIndicator');
        this.toolIndicator = document.getElementById('toolIndicator');
        this.historyBar = document.getElementById('historyBar');
        this.modeSwitch = document.getElementById('modeSwitch');
        this.chatLabel = document.getElementById('chatLabel');
        this.agentLabel = document.getElementById('agentLabel');
        this.dropdownMenu = document.getElementById('dropdownMenu');
        
        // 绑定事件
        this.bindEvents();
        
        // 设置欢迎消息时间
        this.setWelcomeTime();
        
        // 初始化状态
        this.updateStatus('在线', 'online');

        // 初始化模式切换
        this.initModeSwitch();

        // 设置WebView2消息监听器
        this.setupWebView2Listener();

        // 初始化对话管理
        this.initConversationManagement();
    }

    /**
     * 初始化模式切换
     */
    initModeSwitch() {
        if (this.modeSwitch) {
            this.modeSwitch.addEventListener('change', () => this.handleModeSwitch());
        }

        // 初始化模式标签状态
        this.updateModeLabels();
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        // 发送按钮点击事件
        if (this.sendButton) {
            this.sendButton.addEventListener('click', () => this.handleSendMessage());
        }

        // 输入框键盘事件
        if (this.messageInput) {
            this.messageInput.addEventListener('keydown', (e) => this.handleKeyDown(e));
            this.messageInput.addEventListener('input', () => this.handleInputChange());
        }

        // 停止按钮事件
        const stopButton = document.getElementById('stopButton');
        if (stopButton) {
            stopButton.addEventListener('click', () => this.handleStopGeneration());
        }

        // 附件按钮事件
        const attachBtn = document.getElementById('attachBtn');
        const fileInput = document.getElementById('fileInput');
        if (attachBtn && fileInput) {
            attachBtn.addEventListener('click', () => this.handleAttachFile());
            fileInput.addEventListener('change', (e) => this.handleFileSelected(e));
        }

        // 点击外部关闭下拉菜单
        document.addEventListener('click', (e) => {
            if (!e.target.closest('.menu-dropdown')) {
                this.hideDropdownMenu();
            }
            if (!e.target.closest('.control-btn') || !e.target.closest('#historyToggle')) {
                this.hideHistoryBar();
            }
        });
    }

    /**
     * 处理键盘事件
     */
    handleKeyDown(e) {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            this.handleSendMessage();
        }
    }

    /**
     * 处理输入变化
     */
    handleInputChange() {
        // 自动调整输入框高度
        this.autoResizeTextarea();
    }

    /**
     * 自动调整输入框高度
     */
    autoResizeTextarea() {
        if (this.messageInput) {
            this.messageInput.style.height = 'auto';
            this.messageInput.style.height = Math.min(this.messageInput.scrollHeight, 120) + 'px';
        }
    }

    /**
     * 处理发送消息
     */
    handleSendMessage() {
        const message = this.messageInput.value.trim();
        if (!message || this.isGenerating) return;

        // 重置流式消息状态，确保新的AI回复创建新消息
        this.currentStreamingMessage = null;
        this.streamingContent = '';

        // 不立即添加用户消息，等待后端确认

        // 清空输入框
        this.messageInput.value = '';
        this.autoResizeTextarea();

        // 发送消息到C#后端
        this.sendMessageToBackend(message);

        // 更新UI状态
        this.setGeneratingState(true);
    }

    /**
     * 发送消息到C#后端
     */
    sendMessageToBackend(message) {
        try {
            if (window.chrome && window.chrome.webview) {
                const messageData = {
                    action: 'sendMessage',
                    content: message,
                    mode: this.currentMode,
                    conversationId: this.currentConversationId,
                    timestamp: new Date().toISOString()
                };

                window.chrome.webview.postMessage(JSON.stringify(messageData));
            } else {
                console.error('WebView2通信不可用');
                this.addMessage('assistant', '抱歉，无法连接到AI服务。请检查WebView2环境。');
                this.setGeneratingState(false);
            }
        } catch (error) {
            console.error('发送消息到后端失败:', error);
            this.addMessage('assistant', '发送消息时出现错误，请重试。');
            this.setGeneratingState(false);
        }
    }

    /**
     * 设置生成状态
     */
    setGeneratingState(isGenerating) {
        this.isGenerating = isGenerating;
        
        const sendButton = document.getElementById('sendButton');
        const stopButton = document.getElementById('stopButton');
        
        if (isGenerating) {
            if (sendButton) sendButton.style.display = 'none';
            if (stopButton) stopButton.style.display = 'flex';
            this.updateStatus('正在生成...', 'generating');
        } else {
            if (sendButton) sendButton.style.display = 'flex';
            if (stopButton) stopButton.style.display = 'none';
            this.updateStatus('在线', 'online');
        }
    }

    /**
     * 处理停止生成
     */
    handleStopGeneration() {
        if (window.chrome && window.chrome.webview) {
            window.chrome.webview.postMessage(JSON.stringify({
                action: 'stopGeneration'
            }));
        }
        
        this.setGeneratingState(false);
    }

    /**
     * 处理附件
     */
    handleAttachFile() {
        const fileInput = document.getElementById('fileInput');
        if (fileInput) {
            fileInput.click();
        }
    }

    /**
     * 处理文件选择
     */
    handleFileSelected(event) {
        const files = event.target.files;
        if (files && files.length > 0) {

            // 发送文件信息到后端
            const fileInfos = Array.from(files).map(file => ({
                name: file.name,
                size: file.size,
                type: file.type,
                lastModified: file.lastModified
            }));

            if (window.chrome && window.chrome.webview) {
                window.chrome.webview.postMessage(JSON.stringify({
                    action: 'filesSelected',
                    files: fileInfos
                }));
            }

            // 显示选择的文件
            this.displaySelectedFiles(files);
        }
    }

    /**
     * 显示选择的文件
     */
    displaySelectedFiles(files) {
        // 这里可以添加显示选择文件的UI逻辑
    }

    /**
     * 添加消息到界面
     */
    addMessage(type, content, options = {}) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${type}`;
        messageDiv.id = `message-${++this.messageCount}`;

        const contentDiv = document.createElement('div');
        contentDiv.className = 'message-content';

        if (type === 'user') {
            contentDiv.textContent = content;
        } else {
            contentDiv.innerHTML = this.formatContent(content);
            // 处理代码占位符
            this.processCodePlaceholders(contentDiv);
        }

        messageDiv.appendChild(contentDiv);
        this.messagesContainer.appendChild(messageDiv);

        // 滚动到底部
        this.scrollToBottom();

        return messageDiv;
    }

    /**
     * 格式化内容
     */
    formatContent(content) {
        if (!content) return '';

        // 处理代码块 - 标记为占位符，稍后处理
        content = content.replace(/```(\w+)?\n([\s\S]*?)```/g, (match, language, code) => {
            const lang = language || 'text';
            const escapedCode = this.escapeHtml(code.trim());
            const moduleId = `code-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

            // 返回占位符，稍后会被替换为实际的模块
            return `<div class="code-placeholder" data-lang="${lang}" data-code="${encodeURIComponent(escapedCode)}" data-module-id="${moduleId}"></div>`;
        });

        // 处理行内代码
        content = content.replace(/`([^`]+)`/g, '<code class="inline-code">$1</code>');

        // 处理其他Markdown格式
        content = content
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>')
            .replace(/\n/g, '<br>');

        return content;
    }

    /**
     * 转义HTML字符
     */
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    /**
     * 获取当前AI消息（排除欢迎消息）
     */
    getCurrentAIMessage() {
        const messages = this.messagesContainer.querySelectorAll('.message.assistant');
        // 过滤掉欢迎消息，只获取用户会话中的AI消息
        const sessionMessages = Array.from(messages).filter(msg => {
            // 如果消息ID包含welcome或者是第一个消息，则认为是欢迎消息
            return !msg.id.includes('welcome') && !msg.classList.contains('welcome-message');
        });
        return sessionMessages.length > 0 ? sessionMessages[sessionMessages.length - 1] : null;
    }

    /**
     * 处理代码占位符，转换为实际的代码模块
     */
    processCodePlaceholders(container) {
        const placeholders = container.querySelectorAll('.code-placeholder');
        placeholders.forEach(placeholder => {
            const lang = placeholder.getAttribute('data-lang');
            const code = decodeURIComponent(placeholder.getAttribute('data-code'));
            const moduleId = placeholder.getAttribute('data-module-id');

            // 创建代码模块
            const codeModule = this.createCodeModule(moduleId, lang, code);

            // 替换占位符
            placeholder.parentNode.replaceChild(codeModule, placeholder);
        });
    }

    /**
     * 创建代码模块元素
     */
    createCodeModule(moduleId, language, code) {
        const module = document.createElement('div');
        module.className = 'module code-module';
        module.id = moduleId;

        module.innerHTML = `
            <div class="module-header" onclick="toggleModule('${moduleId}')">
                <span class="module-expand-icon">▶</span>
                <span class="module-icon">💻</span>
                <span class="module-name">代码 (${language})</span>
                <button class="action-btn copy-btn" onclick="copyCode('${moduleId}')" title="复制代码">📋</button>
            </div>
            <div class="module-content">
                <pre><code class="language-${language}">${code}</code></pre>
            </div>
        `;

        return module;
    }

    /**
     * 从消息中获取纯文本内容（排除模块）
     */
    getTextContentFromMessage(messageElement) {
        const contentDiv = messageElement.querySelector('.message-content');
        if (!contentDiv) return '';

        // 克隆内容，移除所有模块，获取纯文本
        const clone = contentDiv.cloneNode(true);
        const modules = clone.querySelectorAll('.module');
        modules.forEach(module => module.remove());

        return clone.textContent || '';
    }

    /**
     * 更新消息的文本内容，保留模块
     */
    updateMessageTextContent(messageElement, textContent, isStreaming = false) {
        const contentDiv = messageElement.querySelector('.message-content');
        if (!contentDiv) return;

        // 查找或创建文本容器
        let textContainer = contentDiv.querySelector('.message-text');
        if (!textContainer) {
            textContainer = document.createElement('div');
            textContainer.className = 'message-text';
            // 将文本容器添加到内容区域，模块会在后面添加
            contentDiv.appendChild(textContainer);
        }

        if (isStreaming) {
            // 流式更新期间：只更新纯文本，不处理代码块
            // 使用简单的HTML格式化，但跳过代码占位符处理
            textContainer.innerHTML = this.formatContentWithoutCodeBlocks(textContent);
        } else {
            // 非流式更新：正常处理包括代码块
            textContainer.innerHTML = this.formatContent(textContent);
            this.processCodePlaceholders(textContainer);
        }
    }

    /**
     * 格式化内容但不处理代码块（用于流式更新）
     */
    formatContentWithoutCodeBlocks(content) {
        if (!content) return '';

        // 基本的HTML转义和格式化，但不创建代码占位符
        return content
            .replace(/&/g, '&amp;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            .replace(/\n/g, '<br>')
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>');
    }

    /**
     * 滚动到底部
     */
    scrollToBottom() {
        if (this.messagesContainer) {
            this.messagesContainer.scrollTop = this.messagesContainer.scrollHeight;
        }
    }



    /**
     * 更新状态
     */
    updateStatus(text, type) {
        if (this.statusText) {
            this.statusText.textContent = text;
        }

        if (this.statusIndicator) {
            switch (type) {
                case 'online':
                    this.statusIndicator.textContent = '🟢';
                    break;
                case 'generating':
                    this.statusIndicator.textContent = '🟡';
                    break;
                case 'error':
                    this.statusIndicator.textContent = '🔴';
                    break;
                default:
                    this.statusIndicator.textContent = '⚪';
            }
        }
    }

    /**
     * 处理模式切换
     */
    handleModeSwitch() {
        this.currentMode = this.modeSwitch.checked ? 'Agent' : 'Chat';
        this.updateModeLabels();

        // 通知后端模式变化
        if (window.chrome && window.chrome.webview) {
            window.chrome.webview.postMessage(JSON.stringify({
                action: 'modeChanged',
                mode: this.currentMode
            }));
        }
    }

    /**
     * 更新模式标签
     */
    updateModeLabels() {
        if (this.chatLabel && this.agentLabel) {
            if (this.currentMode === 'Agent') {
                this.chatLabel.classList.remove('active');
                this.agentLabel.classList.add('active');
                // 为Agent模式添加CSS类，优化消息显示
                this.messagesContainer.classList.add('agent-mode');
            } else {
                this.chatLabel.classList.add('active');
                this.agentLabel.classList.remove('active');
                // 移除Agent模式的CSS类
                this.messagesContainer.classList.remove('agent-mode');
            }
        }
    }

    /**
     * 处理模式切换确认
     */
    handleModeChangedConfirmation(data) {
        if (data.mode) {
            this.currentMode = data.mode;
            // 更新开关状态
            if (this.modeSwitch) {
                this.modeSwitch.checked = (data.mode === 'Agent');
            }
            this.updateModeLabels();
        }
    }

    /**
     * 设置WebView2消息监听器
     */
    setupWebView2Listener() {
        if (window.chrome && window.chrome.webview) {
            window.chrome.webview.addEventListener('message', (event) => {
                try {
                    const data = JSON.parse(event.data);
                    this.handleBackendMessage(data);
                } catch (error) {
                    console.error('解析后端消息失败:', error, '原始数据:', event.data);
                }
            });
        } else {
            // 在非WebView2环境中显示提示
            this.updateStatus('离线', 'error');
        }
    }

    /**
     * 处理后端消息
     */
    handleBackendMessage(data) {
        switch (data.action) {
            case 'initMessage':
                this.handleInitMessage(data);
                break;
            case 'messageReceived':
                this.handleMessageReceived(data);
                break;
            case 'aiResponse':
                this.handleAiResponse(data);
                break;
            case 'reactProgress':
                this.handleReactProgress(data);
                break;
            case 'streamingUpdate':
                this.handleStreamingUpdate(data);
                break;
            case 'streamingComplete':
                this.handleStreamingComplete(data);
                break;
            case 'toolExecution':
                this.handleToolExecution(data);
                break;
            case 'error':
                this.handleError(data);
                break;
            case 'statusUpdate':
                this.handleStatusUpdate(data);
                break;
            case 'modeChanged':
                this.handleModeChangedConfirmation(data);
                break;
            case 'historyData':
                this.handleHistoryData(data);
                break;
            case 'sessionSwitched':
                this.handleSessionSwitched(data);
                break;
            case 'clearMessages':
                this.handleClearMessages(data);
                break;
            case 'sessionMessage':
                this.handleSessionMessage(data);
                break;
            case 'userMessage':
                this.handleUserMessage(data);
                break;
            case 'userMessageConfirmed':
                this.handleUserMessageConfirmed(data);
                break;
            default:
                break;
        }
    }

    /**
     * 处理初始化消息
     */
    handleInitMessage(data) {
        // 只处理AI消息，用户消息已在前端添加
        if (!data.isUser) {
            this.addMessage('assistant', data.content);
        }
    }

    /**
     * 处理消息接收
     */
    handleMessageReceived(data) {
        this.addMessage('assistant', data.content);
        this.setGeneratingState(false);
    }

    /**
     * 处理AI回复
     */
    handleAiResponse(data) {

        // 如果当前有流式消息，更新它；否则创建新消息
        if (this.currentStreamingMessage) {
            const contentDiv = this.currentStreamingMessage.querySelector('.message-content');
            if (contentDiv) {
                contentDiv.innerHTML = this.formatContent(data.content);
            }
            this.currentStreamingMessage = null;
            this.streamingContent = '';
        } else {
            this.addMessage('assistant', data.content);
        }

        this.setGeneratingState(false);
        this.scrollToBottom();
    }

    /**
     * 处理ReAct进度（不触发完成状态）
     */
    handleReactProgress(data) {
        // 添加ReAct进度消息，但不改变生成状态
        this.addMessage('assistant', data.content);
        this.scrollToBottom();

        // 保持生成状态，不调用setGeneratingState(false)
        console.log('[ReAct进度] 添加进度消息，保持生成状态');
    }

    /**
     * 处理流式更新
     */
    handleStreamingUpdate(data) {
        // 检查是否有当前的AI消息，如果没有则创建新的AI消息
        if (!this.currentStreamingMessage) {
            // 创建新的AI消息用于流式输出
            this.currentStreamingMessage = this.addMessage('assistant', '');
            this.streamingContent = '';
            console.log('[流式更新] 创建新的AI消息用于流式输出');
        }

        // 追加新内容
        this.streamingContent += data.content;

        // 更新文本内容，保留模块（流式更新模式）
        this.updateMessageTextContent(this.currentStreamingMessage, this.streamingContent, true);

        this.scrollToBottom();
    }

    /**
     * 处理流式完成
     */
    handleStreamingComplete() {
        // 流式完成时，最终处理代码块
        if (this.currentStreamingMessage && this.streamingContent) {
            this.updateMessageTextContent(this.currentStreamingMessage, this.streamingContent, false);
        }

        this.currentStreamingMessage = null;
        this.streamingContent = '';
        this.setGeneratingState(false);
    }

    /**
     * 处理工具执行
     */
    handleToolExecution(data) {

        if (data.result && data.result.isComplete) {
            // 工具执行完成
            if (this.currentToolModuleId) {
                this.moduleSystem.updateModule(this.currentToolModuleId, {
                    status: data.result.isSuccess ? 'success' : 'error',
                    result: data.result.result || '执行完成'
                });
                this.currentToolModuleId = null;
            }
            this.hideToolIndicator();
        } else if (data.result && data.result.isStart) {
            // 工具开始执行
            this.showToolIndicator(data.toolName || '正在调用工具...');

            // 创建工具模块并添加到当前AI消息中
            if (this.moduleSystem) {
                const module = this.moduleSystem.createToolModule({
                    toolName: data.toolName,
                    description: data.result.description || '正在执行工具...',
                    parameters: data.result.parameters,
                    status: 'running'
                });

                // 工具块应该插入到最后一个AI消息之后，作为独立的位置
                let targetMessage = null;

                // 查找最后一个AI消息
                const messages = this.messagesContainer.querySelectorAll('.message.assistant');
                if (messages.length > 0) {
                    targetMessage = messages[messages.length - 1];
                    console.log('[工具执行] 找到最后一个AI消息，将在其后插入工具块');
                } else {
                    // 如果没有AI消息，创建新的
                    console.log('[工具执行] 创建新的AI消息用于工具模块');
                    targetMessage = this.addMessage('assistant', '');
                }

                // 添加模块到目标消息中
                const messageElement = targetMessage.querySelector('.message-content');
                if (messageElement) {
                    // 直接添加到消息内容的末尾
                    messageElement.appendChild(module.element);
                    console.log('[工具执行] 工具模块已添加到消息末尾');
                }

                this.currentToolModuleId = module.id;

                // 滚动到底部
                this.scrollToBottom();
            }
        }
    }

    /**
     * 获取工具类型
     */
    getToolType(toolName) {
        if (!toolName) return 'info';

        const toolName_lower = toolName.toLowerCase();
        if (toolName_lower.includes('analysis') || toolName_lower.includes('分析')) {
            return 'gis-analysis';
        } else if (toolName_lower.includes('query') || toolName_lower.includes('查询')) {
            return 'data-query';
        } else if (toolName_lower.includes('calculate') || toolName_lower.includes('计算')) {
            return 'calculation';
        } else {
            return 'info';
        }
    }

    /**
     * 处理错误
     */
    handleError(data) {
        this.addMessage('assistant', `错误: ${data.message}`);
        this.setGeneratingState(false);
        this.updateStatus('错误', 'error');
    }

    /**
     * 处理状态更新
     */
    handleStatusUpdate(data) {
        this.updateStatus(data.status, data.type || 'online');
    }

    /**
     * 处理历史数据
     */
    handleHistoryData(data) {
        this.updateHistoryBar(data.data);
    }

    /**
     * 处理会话切换
     */
    handleSessionSwitched() {
        // 清空当前消息
        this.messagesContainer.innerHTML = '';
        // 隐藏历史记录栏
        this.hideHistoryBar();
    }

    /**
     * 处理清空消息
     */
    handleClearMessages() {
        if (this.messagesContainer) {
            // 保留欢迎消息，只清空会话消息
            const welcomeMessage = this.messagesContainer.querySelector('.welcome-message');
            this.messagesContainer.innerHTML = '';
            if (welcomeMessage) {
                this.messagesContainer.appendChild(welcomeMessage);
            }
        }
        this.currentStreamingMessage = null;
        this.streamingContent = '';
        this.messageCount = 0; // 重置消息计数
    }

    /**
     * 处理会话消息
     */
    handleSessionMessage(data) {
        // 处理所有消息（用户消息和AI消息）
        if (data.isUser) {
            this.addMessage('user', data.content);
        } else {
            this.addMessage('assistant', data.content);
        }
    }

    /**
     * 处理用户消息（从后端发送）
     */
    handleUserMessage() {
        // 用户消息已在前端添加，这里不需要重复添加
    }

    /**
     * 处理用户消息确认（从后端发送）
     */
    handleUserMessageConfirmed(data) {
        this.addMessage('user', data.content);

        // 重置流式状态，确保下一个AI回复创建新消息
        this.currentStreamingMessage = null;
        this.streamingContent = '';
    }

    /**
     * 更新历史记录栏
     */
    updateHistoryBar(historyItems) {
        if (!this.historyBar) return;

        const historyContent = this.historyBar.querySelector('.history-content');
        if (!historyContent) return;

        // 清空现有内容
        historyContent.innerHTML = '';

        // 添加历史记录项
        historyItems.forEach(item => {
            const historyItem = document.createElement('div');
            historyItem.className = `history-item ${item.isActive ? 'active' : ''}`;
            historyItem.innerHTML = `
                <div style="display: flex; justify-content: space-between; align-items: center;">
                    <span>📝 ${item.title}</span>
                    <small style="opacity: 0.7;">${item.timestamp}</small>
                </div>
            `;

            // 添加点击事件
            historyItem.addEventListener('click', () => {
                this.switchToSession(item.id);
            });

            historyContent.appendChild(historyItem);
        });
    }

    /**
     * 切换到指定会话
     */
    switchToSession(sessionId) {
        if (window.chrome && window.chrome.webview) {
            window.chrome.webview.postMessage(JSON.stringify({
                action: 'switchSession',
                sessionId: sessionId
            }));
        }
    }

    /**
     * 显示工具指示器
     */
    showToolIndicator(text) {
        if (this.toolIndicator) {
            const textElement = document.getElementById('toolIndicatorText');
            if (textElement) {
                textElement.textContent = text || '正在调用工具...';
            }
            this.toolIndicator.classList.add('show');
        }
    }

    /**
     * 隐藏工具指示器
     */
    hideToolIndicator() {
        if (this.toolIndicator) {
            this.toolIndicator.classList.remove('show');
        }
    }



    /**
     * 初始化对话管理
     */
    initConversationManagement() {
        // 生成新的对话ID
        this.currentConversationId = this.generateConversationId();
    }

    /**
     * 生成对话ID
     */
    generateConversationId() {
        return 'conv_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11);
    }

    /**
     * 设置欢迎消息时间
     */
    setWelcomeTime() {
        // 可以在这里设置欢迎消息的时间戳
    }

    /**
     * 隐藏下拉菜单
     */
    hideDropdownMenu() {
        if (this.dropdownMenu) {
            this.dropdownMenu.classList.remove('show');
        }
    }

    /**
     * 隐藏历史记录栏
     */
    hideHistoryBar() {
        if (this.historyBar) {
            this.historyBar.classList.remove('show');
        }
    }
}

// 全局函数
function handleHistoryClick() {
    const historyBar = document.getElementById('historyBar');
    if (historyBar) {
        if (historyBar.classList.contains('show')) {
            historyBar.classList.remove('show');
        } else {
            // 请求历史数据
            if (window.chrome && window.chrome.webview) {
                window.chrome.webview.postMessage(JSON.stringify({
                    action: 'getHistory'
                }));
            }
            historyBar.classList.add('show');
        }
    }
}

function handleMenuClick() {
    const dropdownMenu = document.getElementById('dropdownMenu');
    if (dropdownMenu) {
        dropdownMenu.classList.toggle('show');
    }
}

function handleNewChatClick() {
    // 发送新建对话消息到后端
    if (window.chrome && window.chrome.webview) {
        window.chrome.webview.postMessage(JSON.stringify({
            action: 'newChat'
        }));
    }
    // 清空前端消息
    if (window.chatInterface) {
        window.chatInterface.messagesContainer.innerHTML = '';
        window.chatInterface.hideDropdownMenu();
    }
}

function handleClearChatClick() {
    if (confirm('确定要清空当前对话吗？')) {
        // 发送清空对话消息到后端
        if (window.chrome && window.chrome.webview) {
            window.chrome.webview.postMessage(JSON.stringify({
                action: 'clearChat'
            }));
        }
        // 清空前端消息
        if (window.chatInterface) {
            window.chatInterface.messagesContainer.innerHTML = '';
            window.chatInterface.hideDropdownMenu();
        }
    }
}

function handleModelSettingsClick() {
    // 发送模型设置消息到后端
    if (window.chrome && window.chrome.webview) {
        window.chrome.webview.postMessage(JSON.stringify({
            action: 'openModelSettings'
        }));
    }
    closeDropdown();
}

function closeDropdown() {
    const dropdownMenu = document.getElementById('dropdownMenu');
    if (dropdownMenu) {
        dropdownMenu.classList.remove('show');
    }
}

/**
 * 复制代码到剪贴板
 */
function copyCode(moduleIdOrButton) {
    let codeElement;
    let button;

    if (typeof moduleIdOrButton === 'string') {
        // 新的模块系统：通过模块ID查找
        const module = document.getElementById(moduleIdOrButton);
        if (module) {
            codeElement = module.querySelector('code');
            button = module.querySelector('.copy-btn');
        }
    } else {
        // 旧的系统：直接传递按钮元素
        button = moduleIdOrButton;
        const codeBlock = button.closest('.code-block, .module');
        codeElement = codeBlock.querySelector('code');
    }

    if (!codeElement || !button) return;

    const text = codeElement.textContent;

    navigator.clipboard.writeText(text).then(() => {
        button.textContent = '✅';
        setTimeout(() => {
            button.textContent = '📋';
        }, 2000);
    }).catch(() => {
        // 降级方案
        const textArea = document.createElement('textarea');
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);

        button.textContent = '✅';
        setTimeout(() => {
            button.textContent = '📋';
        }, 2000);
    });
}

function closeModal() {
    const modalOverlay = document.getElementById('modalOverlay');
    if (modalOverlay) {
        modalOverlay.classList.remove('show');
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    window.chatInterface = new ChatInterface();
});
