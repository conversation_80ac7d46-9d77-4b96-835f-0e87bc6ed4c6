package com.example.babylog.ui.screens

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.navigation.NavController
import com.example.babylog.data.database.BabyLogDatabase
import com.example.babylog.data.entity.Baby
import com.example.babylog.data.repository.BabyRepository
import com.example.babylog.navigation.Screen
import com.example.babylog.ui.theme.*
import java.text.SimpleDateFormat
import java.util.*

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun HomeScreen(navController: NavController) {
    val context = LocalContext.current
    val database = BabyLogDatabase.getDatabase(context)
    val repository = BabyRepository(
        database.babyDao(),
        database.healthRecordDao(),
        database.milestoneDao(),
        database.feedingRecordDao(),
        database.photoDao()
    )

    val babies by repository.getAllBabies().collectAsState(initial = emptyList())
    var showAddDialog by remember { mutableStateOf(false) }

    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(NeumorphismColors.background)
    ) {
        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = 20.dp),
            contentPadding = PaddingValues(vertical = 20.dp),
            verticalArrangement = Arrangement.spacedBy(20.dp)
        ) {
            // Header
            item {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Column {
                        Text(
                            text = "宝贝日记",
                            style = MaterialTheme.typography.headlineLarge,
                            fontWeight = FontWeight.Bold,
                            color = NeumorphismColors.textPrimary
                        )
                        Text(
                            text = "记录每一个珍贵时刻",
                            style = MaterialTheme.typography.bodyMedium,
                            color = NeumorphismColors.textSecondary
                        )
                    }
                }
            }

            // 今日统计卡片
            if (babies.isNotEmpty()) {
                item {
                    TodayStatsSection(repository, babies.first())
                }
            }

            // 宝宝卡片
            if (babies.isEmpty()) {
                item {
                    EmptyStateCard(
                        onClick = { navController.navigate("${Screen.AddBaby.route}?babyId=") }
                    )
                }
            } else {
                item {
                    Text(
                        text = "我的宝宝",
                        style = MaterialTheme.typography.titleLarge,
                        fontWeight = FontWeight.Bold,
                        color = NeumorphismColors.textPrimary,
                        modifier = Modifier.padding(vertical = 8.dp)
                    )
                }

                items(babies) { baby ->
                    ModernBabyCard(
                        baby = baby,
                        repository = repository,
                        onClick = {
                            // Navigate to baby details
                        }
                    )
                }
            }

            // 快速操作区域
            if (babies.isNotEmpty()) {
                item {
                    QuickActionsSection(navController, babies.first())
                }
            }

            // 底部间距，避免被悬浮按钮遮挡
            item {
                Spacer(modifier = Modifier.height(80.dp))
            }
        }

        // 悬浮按钮
        NeumorphismFloatingActionButton(
            onClick = { showAddDialog = true },
            modifier = Modifier
                .align(Alignment.BottomEnd)
                .padding(20.dp)
        ) {
            Icon(
                Icons.Default.Add,
                contentDescription = "添加记录",
                tint = Color.White
            )
        }
    }

    // 添加记录对话框
    if (showAddDialog) {
        AddRecordDialog(
            babies = babies,
            onDismiss = { showAddDialog = false },
            onNavigate = { route ->
                showAddDialog = false
                navController.navigate(route)
            }
        )
    }
}

// 今日统计区域
@Composable
fun TodayStatsSection(repository: BabyRepository, baby: Baby) {
    val today = remember {
        Calendar.getInstance().apply {
            set(Calendar.HOUR_OF_DAY, 0)
            set(Calendar.MINUTE, 0)
            set(Calendar.SECOND, 0)
            set(Calendar.MILLISECOND, 0)
        }.time
    }

    val feedingRecords by repository.getFeedingRecordsByBaby(baby.id).collectAsState(initial = emptyList())
    val todayFeedings = feedingRecords.filter { it.startTime >= today }

    Column {
        Text(
            text = "今日概览",
            style = MaterialTheme.typography.titleLarge,
            fontWeight = FontWeight.Bold,
            color = NeumorphismColors.textPrimary,
            modifier = Modifier.padding(vertical = 8.dp)
        )

        LazyRow(
            horizontalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            item {
                StatCard(
                    title = "喂养次数",
                    value = "${todayFeedings.size}",
                    subtitle = "次",
                    color = NeumorphismColors.primary,
                    modifier = Modifier.width(120.dp)
                )
            }

            item {
                val totalAmount = todayFeedings.filter { it.type == "bottle" }
                    .mapNotNull { it.amount }.sum()
                StatCard(
                    title = "奶量",
                    value = "${totalAmount.toInt()}",
                    subtitle = "ml",
                    color = NeumorphismColors.secondary,
                    modifier = Modifier.width(120.dp)
                )
            }

            item {
                val ageInDays = ((Date().time - baby.birthDate.time) / (1000 * 60 * 60 * 24)).toInt()
                StatCard(
                    title = "成长天数",
                    value = "$ageInDays",
                    subtitle = "天",
                    color = NeumorphismColors.accent,
                    modifier = Modifier.width(120.dp)
                )
            }
        }
    }
}

// 空状态卡片
@Composable
fun EmptyStateCard(onClick: () -> Unit) {
    NeumorphismCard(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 20.dp),
        elevation = 8,
        cornerRadius = 24
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier.padding(24.dp)
        ) {
            Icon(
                Icons.Default.ChildCare,
                contentDescription = null,
                modifier = Modifier.size(64.dp),
                tint = NeumorphismColors.primary
            )
            Spacer(modifier = Modifier.height(16.dp))
            Text(
                text = "开始记录宝宝的成长",
                style = MaterialTheme.typography.titleLarge,
                fontWeight = FontWeight.Bold,
                color = NeumorphismColors.textPrimary
            )
            Spacer(modifier = Modifier.height(8.dp))
            Text(
                text = "添加第一个宝宝档案，开启美好的记录之旅",
                style = MaterialTheme.typography.bodyMedium,
                color = NeumorphismColors.textSecondary
            )
            Spacer(modifier = Modifier.height(20.dp))
            NeumorphismButton(
                onClick = onClick,
                modifier = Modifier.fillMaxWidth()
            ) {
                Icon(Icons.Default.Add, contentDescription = null)
                Spacer(modifier = Modifier.width(8.dp))
                Text("添加宝宝档案")
            }
        }
    }
}

// 现代化宝宝卡片
@Composable
fun ModernBabyCard(
    baby: Baby,
    repository: BabyRepository,
    onClick: () -> Unit
) {
    val ageInDays = ((Date().time - baby.birthDate.time) / (1000 * 60 * 60 * 24)).toInt()
    val ageText = when {
        ageInDays < 30 -> "${ageInDays}天"
        ageInDays < 365 -> "${ageInDays / 30}个月"
        else -> "${ageInDays / 365}岁${(ageInDays % 365) / 30}个月"
    }

    NeumorphismCard(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onClick() },
        elevation = 6,
        cornerRadius = 20
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = baby.name,
                    style = MaterialTheme.typography.titleLarge,
                    fontWeight = FontWeight.Bold,
                    color = NeumorphismColors.textPrimary
                )
                Spacer(modifier = Modifier.height(4.dp))
                Text(
                    text = ageText,
                    style = MaterialTheme.typography.bodyLarge,
                    color = NeumorphismColors.primary,
                    fontWeight = FontWeight.Medium
                )
                Text(
                    text = SimpleDateFormat("yyyy年MM月dd日", Locale.getDefault()).format(baby.birthDate),
                    style = MaterialTheme.typography.bodySmall,
                    color = NeumorphismColors.textSecondary
                )
            }

            Box(
                modifier = Modifier
                    .size(60.dp)
                    .clip(RoundedCornerShape(30.dp))
                    .background(
                        if (baby.gender == "male") NeumorphismColors.primary.copy(alpha = 0.1f)
                        else NeumorphismColors.secondary.copy(alpha = 0.1f)
                    ),
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    Icons.Default.ChildCare,
                    contentDescription = baby.gender,
                    modifier = Modifier.size(32.dp),
                    tint = if (baby.gender == "male") NeumorphismColors.primary else NeumorphismColors.secondary
                )
            }
        }
    }
}

// 快速操作区域
@Composable
fun QuickActionsSection(navController: NavController, baby: Baby) {
    Column {
        Text(
            text = "快速记录",
            style = MaterialTheme.typography.titleLarge,
            fontWeight = FontWeight.Bold,
            color = NeumorphismColors.textPrimary,
            modifier = Modifier.padding(vertical = 8.dp)
        )

        LazyRow(
            horizontalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            item {
                QuickActionCard(
                    title = "喂养",
                    icon = Icons.Default.Restaurant,
                    color = NeumorphismColors.primary,
                    onClick = { navController.navigate("${Screen.AddFeeding.route}?babyId=${baby.id}") }
                )
            }

            item {
                QuickActionCard(
                    title = "健康",
                    icon = Icons.Default.Favorite,
                    color = NeumorphismColors.secondary,
                    onClick = { navController.navigate("${Screen.AddHealth.route}?babyId=${baby.id}") }
                )
            }

            item {
                QuickActionCard(
                    title = "里程碑",
                    icon = Icons.Default.Star,
                    color = NeumorphismColors.accent,
                    onClick = { navController.navigate("${Screen.AddMilestone.route}?babyId=${baby.id}") }
                )
            }
        }
    }
}

// 快速操作卡片
@Composable
fun QuickActionCard(
    title: String,
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    color: Color,
    onClick: () -> Unit
) {
    NeumorphismCard(
        modifier = Modifier
            .width(100.dp)
            .height(100.dp)
            .clickable { onClick() },
        elevation = 4,
        cornerRadius = 16
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center,
            modifier = Modifier.fillMaxSize()
        ) {
            Box(
                modifier = Modifier
                    .size(40.dp)
                    .clip(RoundedCornerShape(20.dp))
                    .background(color.copy(alpha = 0.1f)),
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    icon,
                    contentDescription = title,
                    modifier = Modifier.size(24.dp),
                    tint = color
                )
            }
            Spacer(modifier = Modifier.height(8.dp))
            Text(
                text = title,
                style = MaterialTheme.typography.bodySmall,
                color = NeumorphismColors.textPrimary,
                fontWeight = FontWeight.Medium
            )
        }
    }
}

// 添加记录对话框
@Composable
fun AddRecordDialog(
    babies: List<Baby>,
    onDismiss: () -> Unit,
    onNavigate: (String) -> Unit
) {
    if (babies.isEmpty()) {
        AlertDialog(
            onDismissRequest = onDismiss,
            title = { Text("提示") },
            text = { Text("请先添加宝宝档案") },
            confirmButton = {
                TextButton(onClick = { onNavigate("${Screen.AddBaby.route}?babyId=") }) {
                    Text("添加宝宝")
                }
            },
            dismissButton = {
                TextButton(onClick = onDismiss) {
                    Text("取消")
                }
            }
        )
        return
    }

    val baby = babies.first()

    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text(
                "添加记录",
                style = MaterialTheme.typography.titleLarge,
                fontWeight = FontWeight.Bold
            )
        },
        text = {
            Column {
                Text(
                    "为 ${baby.name} 添加记录",
                    style = MaterialTheme.typography.bodyMedium,
                    color = NeumorphismColors.textSecondary
                )
                Spacer(modifier = Modifier.height(16.dp))

                val actions = listOf(
                    "喂养记录" to "${Screen.AddFeeding.route}?babyId=${baby.id}",
                    "健康记录" to "${Screen.AddHealth.route}?babyId=${baby.id}",
                    "成长里程碑" to "${Screen.AddMilestone.route}?babyId=${baby.id}"
                )

                actions.forEach { (title, route) ->
                    TextButton(
                        onClick = { onNavigate(route) },
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Text(title)
                    }
                }
            }
        },
        confirmButton = {},
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("取消")
            }
        }
    )
}
