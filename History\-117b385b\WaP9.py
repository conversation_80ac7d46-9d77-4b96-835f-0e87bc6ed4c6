#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建眼睛图标
"""

import os
import base64
from pathlib import Path

# 闭合的眼睛图标的Base64编码
# 这是一个简单的24x24像素的PNG图像，包含一个闭合的眼睛图标
EYE_CLOSED_BASE64 = """
iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAACXBIWXMAAAsTAAALEwEAmpwYAAABhUlEQVR4nO2UPUvDUBSGn9OkVRBcBBdHF8FF0MXRRXBxdBJcHJ0EF0dHwT9gcHF0dHR0dHR0dHR0dHR0dHR0dHR0dHR0dHR0dHR0dHR0dHR0dHR0TJJLm9zcNDdNk1xicXFz7jnvOXfOPQL/XKqT4L7vjwKrwDxwA2yIyHs78Z4JfN+fBTaBsVroEfBG5GcnAj2tgr7vTwFbdeIAY8C27/tTnQh0JOB53iJwAPQ1CRkEDj3Pm29XoC1B13WXgD2g1yC0D9j3PG+pHYFQgbo4QC9w6HneUqhAWNE8z9sBxkNSImAduAYmgZWQnHFgN1SgqcBv4lWRA2BGRM6qMc/zNoH5kJx9YFVEPpsJhBXRdd09YKIufg7Mich1Le4Cx8BEk5wTYFlEPsIEopYCFfGqyBUwKyKXNYEZ4BQYapBTycmLyEeYQFiLquJV8VtgWkTuagFVcRUkcAdMi8h9mEBoi2rET4FJEXmoEa+IXAATIvIQJhAqUBVfAxaAIrApIqVa8UpOAdgWkVI74v8CXwQlQpKPUvEgAAAAAElFTkSuQmCC
"""

# 睁开的眼睛图标的Base64编码
# 这是一个简单的24x24像素的PNG图像，包含一个睁开的眼睛图标
EYE_OPEN_BASE64 = """
iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAACXBIWXMAAAsTAAALEwEAmpwYAAABrElEQVR4nO2UO2gUURSGv3NnZ3aTjSIWFhYWFhILCwsLCwsLCwsLCwsLCwsLC8FCsBAsLCwsLCwsLCwsLCwsLCwsLCwsLCwsLCwsLCwsLCwsLCwsLCwsLJnZO3fOLTKbDTF3s8YiD/zNcM//nXPPmQH+c6lOgkejUQ+wBCwA18C6iNw1E2+ZYDQazQKbwGgt9AhYEZGfbgR6WgVHo9EUsF0nDjAGbI9Go6lOBDoSiMfjC8AB0NckZBA4jMfjC+0KtCUYDocvAXtAr0FoH7Afj8df6oTAmCCVSq0Db4zSAWymUql1U4K6K0ilUjvAZK3wOQCzInJZinkBHAMTNXKOgUUR+TAhMN2DinhZyAUwIyKXZYJp4BQYapBT2U5BRD5MCIxaVCVeEb4GpkTktkwwCXwF0F0j5xaYEpF7EwKjFpXETwCMi8h9lXhF5AwYE5F7EwIjgpL4GrAAFIFNESlUiVdy8sCWiBRMCIwIstnsGvAeKAEbIlLIZDIrwIeqnAKwLSIFE4KW/+R/gl8UpUKSj1LxIAAAAABJRU5ErkJggg==
"""

def main():
    """主函数"""
    # 确保resources目录存在
    resources_dir = Path("resources")
    resources_dir.mkdir(exist_ok=True)

    # 解码Base64并保存为PNG文件
    eye_closed_data = base64.b64decode(EYE_CLOSED_BASE64.strip())
    with open(resources_dir / "eye_closed.png", "wb") as f:
        f.write(eye_closed_data)

    eye_open_data = base64.b64decode(EYE_OPEN_BASE64.strip())
    with open(resources_dir / "eye_open.png", "wb") as f:
        f.write(eye_open_data)

    print(f"已创建眼睛图标: {resources_dir / 'eye_closed.png'} 和 {resources_dir / 'eye_open.png'}")

if __name__ == "__main__":
    main()
