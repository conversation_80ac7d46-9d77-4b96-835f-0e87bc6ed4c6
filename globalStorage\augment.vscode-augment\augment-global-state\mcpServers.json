[{"name": "Context 7", "command": "npx -y @upstash/context7-mcp@latest", "arguments": "", "useShellInterpolation": true, "id": "04ec1b2a-f411-4dd0-b9d5-670b6ca2eef3", "tools": [{"definition": {"name": "resolve-library-id_Context_7", "description": "Resolves a package/product name to a Context7-compatible library ID and returns a list of matching libraries.\n\nYou MUST call this function before 'get-library-docs' to obtain a valid Context7-compatible library ID UNLESS the user explicitly provides a library ID in the format '/org/project' or '/org/project/version' in their query.\n\nSelection Process:\n1. Analyze the query to understand what library/package the user is looking for\n2. Return the most relevant match based on:\n- Name similarity to the query (exact matches prioritized)\n- Description relevance to the query's intent\n- Documentation coverage (prioritize libraries with higher Code Snippet counts)\n- Trust score (consider libraries with scores of 7-10 more authoritative)\n\nResponse Format:\n- Return the selected library ID in a clearly marked section\n- Provide a brief explanation for why this library was chosen\n- If multiple good matches exist, acknowledge this but proceed with the most relevant one\n- If no good matches exist, clearly state this and suggest query refinements\n\nFor ambiguous queries, request clarification before proceeding with a best-guess match.", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"libraryName\":{\"type\":\"string\",\"description\":\"Library name to search for and retrieve a Context7-compatible library ID.\"}},\"required\":[\"libraryName\"],\"additionalProperties\":false,\"$schema\":\"http://json-schema.org/draft-07/schema#\"}", "tool_safety": 0, "original_mcp_server_name": "Context 7", "mcp_server_name": "Context_7", "mcp_tool_name": "resolve-library-id"}, "identifier": {"hostName": "mcpHost", "toolId": "resolve-library-id_Context_7"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "get-library-docs_Context_7", "description": "Fetches up-to-date documentation for a library. You must call 'resolve-library-id' first to obtain the exact Context7-compatible library ID required to use this tool, UNLESS the user explicitly provides a library ID in the format '/org/project' or '/org/project/version' in their query.", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"context7CompatibleLibraryID\":{\"type\":\"string\",\"description\":\"Exact Context7-compatible library ID (e.g., '/mongodb/docs', '/vercel/next.js', '/supabase/supabase', '/vercel/next.js/v14.3.0-canary.87') retrieved from 'resolve-library-id' or directly from user query in the format '/org/project' or '/org/project/version'.\"},\"topic\":{\"type\":\"string\",\"description\":\"Topic to focus documentation on (e.g., 'hooks', 'routing').\"},\"tokens\":{\"type\":\"number\",\"description\":\"Maximum number of tokens of documentation to retrieve (default: 10000). Higher values provide more context but consume more tokens.\"}},\"required\":[\"context7CompatibleLibraryID\"],\"additionalProperties\":false,\"$schema\":\"http://json-schema.org/draft-07/schema#\"}", "tool_safety": 0, "original_mcp_server_name": "Context 7", "mcp_server_name": "Context_7", "mcp_tool_name": "get-library-docs"}, "identifier": {"hostName": "mcpHost", "toolId": "get-library-docs_Context_7"}, "isConfigured": true, "enabled": true, "toolSafety": 0}], "disabledTools": [], "disabled": false, "type": "stdio"}, {"name": "Sequential thinking", "command": "npx -y @modelcontextprotocol/server-sequential-thinking", "arguments": "", "useShellInterpolation": true, "id": "6c8a9260-a414-4965-a598-3fcdc2a0a55c", "tools": [{"definition": {"name": "sequentialthinking_Sequential_thinking", "description": "A detailed tool for dynamic and reflective problem-solving through thoughts.\nThis tool helps analyze problems through a flexible thinking process that can adapt and evolve.\nEach thought can build on, question, or revise previous insights as understanding deepens.\n\nWhen to use this tool:\n- Breaking down complex problems into steps\n- Planning and design with room for revision\n- Analysis that might need course correction\n- Problems where the full scope might not be clear initially\n- Problems that require a multi-step solution\n- Tasks that need to maintain context over multiple steps\n- Situations where irrelevant information needs to be filtered out\n\nKey features:\n- You can adjust total_thoughts up or down as you progress\n- You can question or revise previous thoughts\n- You can add more thoughts even after reaching what seemed like the end\n- You can express uncertainty and explore alternative approaches\n- Not every thought needs to build linearly - you can branch or backtrack\n- Generates a solution hypothesis\n- Verifies the hypothesis based on the Chain of Thought steps\n- Repeats the process until satisfied\n- Provides a correct answer\n\nParameters explained:\n- thought: Your current thinking step, which can include:\n* Regular analytical steps\n* Revisions of previous thoughts\n* Questions about previous decisions\n* Realizations about needing more analysis\n* Changes in approach\n* Hypothesis generation\n* Hypothesis verification\n- next_thought_needed: True if you need more thinking, even if at what seemed like the end\n- thought_number: Current number in sequence (can go beyond initial total if needed)\n- total_thoughts: Current estimate of thoughts needed (can be adjusted up/down)\n- is_revision: A boolean indicating if this thought revises previous thinking\n- revises_thought: If is_revision is true, which thought number is being reconsidered\n- branch_from_thought: If branching, which thought number is the branching point\n- branch_id: Identifier for the current branch (if any)\n- needs_more_thoughts: If reaching end but realizing more thoughts needed\n\nYou should:\n1. Start with an initial estimate of needed thoughts, but be ready to adjust\n2. Feel free to question or revise previous thoughts\n3. Don't hesitate to add more thoughts if needed, even at the \"end\"\n4. Express uncertainty when present\n5. Mark thoughts that revise previous thinking or branch into new paths\n6. Ignore information that is irrelevant to the current step\n7. Generate a solution hypothesis when appropriate\n8. Verify the hypothesis based on the Chain of Thought steps\n9. Repeat the process until satisfied with the solution\n10. Provide a single, ideally correct answer as the final output\n11. Only set next_thought_needed to false when truly done and a satisfactory answer is reached", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"thought\":{\"type\":\"string\",\"description\":\"Your current thinking step\"},\"nextThoughtNeeded\":{\"type\":\"boolean\",\"description\":\"Whether another thought step is needed\"},\"thoughtNumber\":{\"type\":\"integer\",\"description\":\"Current thought number\",\"minimum\":1},\"totalThoughts\":{\"type\":\"integer\",\"description\":\"Estimated total thoughts needed\",\"minimum\":1},\"isRevision\":{\"type\":\"boolean\",\"description\":\"Whether this revises previous thinking\"},\"revisesThought\":{\"type\":\"integer\",\"description\":\"Which thought is being reconsidered\",\"minimum\":1},\"branchFromThought\":{\"type\":\"integer\",\"description\":\"Branching point thought number\",\"minimum\":1},\"branchId\":{\"type\":\"string\",\"description\":\"Branch identifier\"},\"needsMoreThoughts\":{\"type\":\"boolean\",\"description\":\"If more thoughts are needed\"}},\"required\":[\"thought\",\"nextThoughtNeeded\",\"thoughtNumber\",\"totalThoughts\"]}", "tool_safety": 0, "original_mcp_server_name": "Sequential thinking", "mcp_server_name": "Sequential_thinking", "mcp_tool_name": "sequentialthinking"}, "identifier": {"hostName": "mcpHost", "toolId": "sequentialthinking_Sequential_thinking"}, "isConfigured": true, "enabled": true, "toolSafety": 0}], "disabledTools": [], "disabled": false, "type": "stdio"}, {"name": "Playwright", "command": "npx -y @executeautomation/playwright-mcp-server", "arguments": "", "useShellInterpolation": true, "id": "be625ad3-9090-452c-ac85-84040eb593e3", "tools": [{"definition": {"name": "start_codegen_session_Playwright", "description": "Start a new code generation session to record Playwright actions", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"options\":{\"type\":\"object\",\"description\":\"Code generation options\",\"properties\":{\"outputPath\":{\"type\":\"string\",\"description\":\"Directory path where generated tests will be saved (use absolute path)\"},\"testNamePrefix\":{\"type\":\"string\",\"description\":\"Prefix to use for generated test names (default: 'GeneratedTest')\"},\"includeComments\":{\"type\":\"boolean\",\"description\":\"Whether to include descriptive comments in generated tests\"}},\"required\":[\"outputPath\"]}},\"required\":[\"options\"]}", "tool_safety": 0, "original_mcp_server_name": "Playwright", "mcp_server_name": "Playwright", "mcp_tool_name": "start_codegen_session"}, "identifier": {"hostName": "mcpHost", "toolId": "start_codegen_session_Playwright"}, "isConfigured": true, "enabled": false, "toolSafety": 0}, {"definition": {"name": "end_codegen_session_Playwright", "description": "End a code generation session and generate the test file", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"sessionId\":{\"type\":\"string\",\"description\":\"ID of the session to end\"}},\"required\":[\"sessionId\"]}", "tool_safety": 0, "original_mcp_server_name": "Playwright", "mcp_server_name": "Playwright", "mcp_tool_name": "end_codegen_session"}, "identifier": {"hostName": "mcpHost", "toolId": "end_codegen_session_Playwright"}, "isConfigured": true, "enabled": false, "toolSafety": 0}, {"definition": {"name": "get_codegen_session_Playwright", "description": "Get information about a code generation session", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"sessionId\":{\"type\":\"string\",\"description\":\"ID of the session to retrieve\"}},\"required\":[\"sessionId\"]}", "tool_safety": 0, "original_mcp_server_name": "Playwright", "mcp_server_name": "Playwright", "mcp_tool_name": "get_codegen_session"}, "identifier": {"hostName": "mcpHost", "toolId": "get_codegen_session_Playwright"}, "isConfigured": true, "enabled": false, "toolSafety": 0}, {"definition": {"name": "clear_codegen_session_Playwright", "description": "Clear a code generation session without generating a test", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"sessionId\":{\"type\":\"string\",\"description\":\"ID of the session to clear\"}},\"required\":[\"sessionId\"]}", "tool_safety": 0, "original_mcp_server_name": "Playwright", "mcp_server_name": "Playwright", "mcp_tool_name": "clear_codegen_session"}, "identifier": {"hostName": "mcpHost", "toolId": "clear_codegen_session_Playwright"}, "isConfigured": true, "enabled": false, "toolSafety": 0}, {"definition": {"name": "playwright_navigate_Playwright", "description": "Navigate to a URL", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"url\":{\"type\":\"string\",\"description\":\"URL to navigate to the website specified\"},\"browserType\":{\"type\":\"string\",\"description\":\"Browser type to use (chromium, firefox, webkit). Defaults to chromium\",\"enum\":[\"chromium\",\"firefox\",\"webkit\"]},\"width\":{\"type\":\"number\",\"description\":\"Viewport width in pixels (default: 1280)\"},\"height\":{\"type\":\"number\",\"description\":\"Viewport height in pixels (default: 720)\"},\"timeout\":{\"type\":\"number\",\"description\":\"Navigation timeout in milliseconds\"},\"waitUntil\":{\"type\":\"string\",\"description\":\"Navigation wait condition\"},\"headless\":{\"type\":\"boolean\",\"description\":\"Run browser in headless mode (default: false)\"}},\"required\":[\"url\"]}", "tool_safety": 0, "original_mcp_server_name": "Playwright", "mcp_server_name": "Playwright", "mcp_tool_name": "playwright_navigate"}, "identifier": {"hostName": "mcpHost", "toolId": "playwright_navigate_Playwright"}, "isConfigured": true, "enabled": false, "toolSafety": 0}, {"definition": {"name": "playwright_screenshot_Playwright", "description": "Take a screenshot of the current page or a specific element", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"name\":{\"type\":\"string\",\"description\":\"Name for the screenshot\"},\"selector\":{\"type\":\"string\",\"description\":\"CSS selector for element to screenshot\"},\"width\":{\"type\":\"number\",\"description\":\"Width in pixels (default: 800)\"},\"height\":{\"type\":\"number\",\"description\":\"Height in pixels (default: 600)\"},\"storeBase64\":{\"type\":\"boolean\",\"description\":\"Store screenshot in base64 format (default: true)\"},\"fullPage\":{\"type\":\"boolean\",\"description\":\"Store screenshot of the entire page (default: false)\"},\"savePng\":{\"type\":\"boolean\",\"description\":\"Save screenshot as PNG file (default: false)\"},\"downloadsDir\":{\"type\":\"string\",\"description\":\"Custom downloads directory path (default: user's Downloads folder)\"}},\"required\":[\"name\"]}", "tool_safety": 0, "original_mcp_server_name": "Playwright", "mcp_server_name": "Playwright", "mcp_tool_name": "playwright_screenshot"}, "identifier": {"hostName": "mcpHost", "toolId": "playwright_screenshot_Playwright"}, "isConfigured": true, "enabled": false, "toolSafety": 0}, {"definition": {"name": "playwright_click_Playwright", "description": "Click an element on the page", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"selector\":{\"type\":\"string\",\"description\":\"CSS selector for the element to click\"}},\"required\":[\"selector\"]}", "tool_safety": 0, "original_mcp_server_name": "Playwright", "mcp_server_name": "Playwright", "mcp_tool_name": "playwright_click"}, "identifier": {"hostName": "mcpHost", "toolId": "playwright_click_Playwright"}, "isConfigured": true, "enabled": false, "toolSafety": 0}, {"definition": {"name": "playwright_iframe_click_Playwright", "description": "Click an element in an iframe on the page", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"iframeSelector\":{\"type\":\"string\",\"description\":\"CSS selector for the iframe containing the element to click\"},\"selector\":{\"type\":\"string\",\"description\":\"CSS selector for the element to click\"}},\"required\":[\"iframeSelector\",\"selector\"]}", "tool_safety": 0, "original_mcp_server_name": "Playwright", "mcp_server_name": "Playwright", "mcp_tool_name": "playwright_iframe_click"}, "identifier": {"hostName": "mcpHost", "toolId": "playwright_iframe_click_Playwright"}, "isConfigured": true, "enabled": false, "toolSafety": 0}, {"definition": {"name": "playwright_fill_Playwright", "description": "fill out an input field", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"selector\":{\"type\":\"string\",\"description\":\"CSS selector for input field\"},\"value\":{\"type\":\"string\",\"description\":\"Value to fill\"}},\"required\":[\"selector\",\"value\"]}", "tool_safety": 0, "original_mcp_server_name": "Playwright", "mcp_server_name": "Playwright", "mcp_tool_name": "playwright_fill"}, "identifier": {"hostName": "mcpHost", "toolId": "playwright_fill_Playwright"}, "isConfigured": true, "enabled": false, "toolSafety": 0}, {"definition": {"name": "playwright_select_Playwright", "description": "Select an element on the page with Select tag", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"selector\":{\"type\":\"string\",\"description\":\"CSS selector for element to select\"},\"value\":{\"type\":\"string\",\"description\":\"Value to select\"}},\"required\":[\"selector\",\"value\"]}", "tool_safety": 0, "original_mcp_server_name": "Playwright", "mcp_server_name": "Playwright", "mcp_tool_name": "playwright_select"}, "identifier": {"hostName": "mcpHost", "toolId": "playwright_select_Playwright"}, "isConfigured": true, "enabled": false, "toolSafety": 0}, {"definition": {"name": "playwright_hover_Playwright", "description": "Hover an element on the page", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"selector\":{\"type\":\"string\",\"description\":\"CSS selector for element to hover\"}},\"required\":[\"selector\"]}", "tool_safety": 0, "original_mcp_server_name": "Playwright", "mcp_server_name": "Playwright", "mcp_tool_name": "playwright_hover"}, "identifier": {"hostName": "mcpHost", "toolId": "playwright_hover_Playwright"}, "isConfigured": true, "enabled": false, "toolSafety": 0}, {"definition": {"name": "playwright_evaluate_Playwright", "description": "Execute JavaScript in the browser console", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"script\":{\"type\":\"string\",\"description\":\"JavaScript code to execute\"}},\"required\":[\"script\"]}", "tool_safety": 0, "original_mcp_server_name": "Playwright", "mcp_server_name": "Playwright", "mcp_tool_name": "playwright_evaluate"}, "identifier": {"hostName": "mcpHost", "toolId": "playwright_evaluate_Playwright"}, "isConfigured": true, "enabled": false, "toolSafety": 0}, {"definition": {"name": "playwright_console_logs_Playwright", "description": "Retrieve console logs from the browser with filtering options", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"type\":{\"type\":\"string\",\"description\":\"Type of logs to retrieve (all, error, warning, log, info, debug)\",\"enum\":[\"all\",\"error\",\"warning\",\"log\",\"info\",\"debug\"]},\"search\":{\"type\":\"string\",\"description\":\"Text to search for in logs (handles text with square brackets)\"},\"limit\":{\"type\":\"number\",\"description\":\"Maximum number of logs to return\"},\"clear\":{\"type\":\"boolean\",\"description\":\"Whether to clear logs after retrieval (default: false)\"}},\"required\":[]}", "tool_safety": 0, "original_mcp_server_name": "Playwright", "mcp_server_name": "Playwright", "mcp_tool_name": "playwright_console_logs"}, "identifier": {"hostName": "mcpHost", "toolId": "playwright_console_logs_Playwright"}, "isConfigured": true, "enabled": false, "toolSafety": 0}, {"definition": {"name": "playwright_close_Playwright", "description": "Close the browser and release all resources", "input_schema_json": "{\"type\":\"object\",\"properties\":{},\"required\":[]}", "tool_safety": 0, "original_mcp_server_name": "Playwright", "mcp_server_name": "Playwright", "mcp_tool_name": "playwright_close"}, "identifier": {"hostName": "mcpHost", "toolId": "playwright_close_Playwright"}, "isConfigured": true, "enabled": false, "toolSafety": 0}, {"definition": {"name": "playwright_get_Playwright", "description": "Perform an HTTP GET request", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"url\":{\"type\":\"string\",\"description\":\"URL to perform GET operation\"}},\"required\":[\"url\"]}", "tool_safety": 0, "original_mcp_server_name": "Playwright", "mcp_server_name": "Playwright", "mcp_tool_name": "playwright_get"}, "identifier": {"hostName": "mcpHost", "toolId": "playwright_get_Playwright"}, "isConfigured": true, "enabled": false, "toolSafety": 0}, {"definition": {"name": "playwright_post_Playwright", "description": "Perform an HTTP POST request", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"url\":{\"type\":\"string\",\"description\":\"URL to perform POST operation\"},\"value\":{\"type\":\"string\",\"description\":\"Data to post in the body\"},\"token\":{\"type\":\"string\",\"description\":\"Bearer token for authorization\"},\"headers\":{\"type\":\"object\",\"description\":\"Additional headers to include in the request\",\"additionalProperties\":{\"type\":\"string\"}}},\"required\":[\"url\",\"value\"]}", "tool_safety": 0, "original_mcp_server_name": "Playwright", "mcp_server_name": "Playwright", "mcp_tool_name": "playwright_post"}, "identifier": {"hostName": "mcpHost", "toolId": "playwright_post_Playwright"}, "isConfigured": true, "enabled": false, "toolSafety": 0}, {"definition": {"name": "playwright_put_Playwright", "description": "Perform an HTTP PUT request", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"url\":{\"type\":\"string\",\"description\":\"URL to perform PUT operation\"},\"value\":{\"type\":\"string\",\"description\":\"Data to PUT in the body\"}},\"required\":[\"url\",\"value\"]}", "tool_safety": 0, "original_mcp_server_name": "Playwright", "mcp_server_name": "Playwright", "mcp_tool_name": "playwright_put"}, "identifier": {"hostName": "mcpHost", "toolId": "playwright_put_Playwright"}, "isConfigured": true, "enabled": false, "toolSafety": 0}, {"definition": {"name": "playwright_patch_Playwright", "description": "Perform an HTTP PATCH request", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"url\":{\"type\":\"string\",\"description\":\"URL to perform PUT operation\"},\"value\":{\"type\":\"string\",\"description\":\"Data to PATCH in the body\"}},\"required\":[\"url\",\"value\"]}", "tool_safety": 0, "original_mcp_server_name": "Playwright", "mcp_server_name": "Playwright", "mcp_tool_name": "playwright_patch"}, "identifier": {"hostName": "mcpHost", "toolId": "playwright_patch_Playwright"}, "isConfigured": true, "enabled": false, "toolSafety": 0}, {"definition": {"name": "playwright_delete_Playwright", "description": "Perform an HTTP DELETE request", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"url\":{\"type\":\"string\",\"description\":\"URL to perform DELETE operation\"}},\"required\":[\"url\"]}", "tool_safety": 0, "original_mcp_server_name": "Playwright", "mcp_server_name": "Playwright", "mcp_tool_name": "playwright_delete"}, "identifier": {"hostName": "mcpHost", "toolId": "playwright_delete_Playwright"}, "isConfigured": true, "enabled": false, "toolSafety": 0}, {"definition": {"name": "playwright_expect_response_Playwright", "description": "Ask Playwright to start waiting for a HTTP response. This tool initiates the wait operation but does not wait for its completion.", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"id\":{\"type\":\"string\",\"description\":\"Unique & arbitrary identifier to be used for retrieving this response later with `Playwright_assert_response`.\"},\"url\":{\"type\":\"string\",\"description\":\"URL pattern to match in the response.\"}},\"required\":[\"id\",\"url\"]}", "tool_safety": 0, "original_mcp_server_name": "Playwright", "mcp_server_name": "Playwright", "mcp_tool_name": "playwright_expect_response"}, "identifier": {"hostName": "mcpHost", "toolId": "playwright_expect_response_Playwright"}, "isConfigured": true, "enabled": false, "toolSafety": 0}, {"definition": {"name": "playwright_assert_response_Playwright", "description": "Wait for and validate a previously initiated HTTP response wait operation.", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"id\":{\"type\":\"string\",\"description\":\"Identifier of the HTTP response initially expected using `Playwright_expect_response`.\"},\"value\":{\"type\":\"string\",\"description\":\"Data to expect in the body of the HTTP response. If provided, the assertion will fail if this value is not found in the response body.\"}},\"required\":[\"id\"]}", "tool_safety": 0, "original_mcp_server_name": "Playwright", "mcp_server_name": "Playwright", "mcp_tool_name": "playwright_assert_response"}, "identifier": {"hostName": "mcpHost", "toolId": "playwright_assert_response_Playwright"}, "isConfigured": true, "enabled": false, "toolSafety": 0}, {"definition": {"name": "playwright_custom_user_agent_Playwright", "description": "Set a custom User Agent for the browser", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"userAgent\":{\"type\":\"string\",\"description\":\"Custom User Agent for the Playwright browser instance\"}},\"required\":[\"userAgent\"]}", "tool_safety": 0, "original_mcp_server_name": "Playwright", "mcp_server_name": "Playwright", "mcp_tool_name": "playwright_custom_user_agent"}, "identifier": {"hostName": "mcpHost", "toolId": "playwright_custom_user_agent_Playwright"}, "isConfigured": true, "enabled": false, "toolSafety": 0}, {"definition": {"name": "playwright_get_visible_text_Playwright", "description": "Get the visible text content of the current page", "input_schema_json": "{\"type\":\"object\",\"properties\":{},\"required\":[]}", "tool_safety": 0, "original_mcp_server_name": "Playwright", "mcp_server_name": "Playwright", "mcp_tool_name": "playwright_get_visible_text"}, "identifier": {"hostName": "mcpHost", "toolId": "playwright_get_visible_text_Playwright"}, "isConfigured": true, "enabled": false, "toolSafety": 0}, {"definition": {"name": "playwright_get_visible_html_Playwright", "description": "Get the HTML content of the current page", "input_schema_json": "{\"type\":\"object\",\"properties\":{},\"required\":[]}", "tool_safety": 0, "original_mcp_server_name": "Playwright", "mcp_server_name": "Playwright", "mcp_tool_name": "playwright_get_visible_html"}, "identifier": {"hostName": "mcpHost", "toolId": "playwright_get_visible_html_Playwright"}, "isConfigured": true, "enabled": false, "toolSafety": 0}, {"definition": {"name": "playwright_go_back_Playwright", "description": "Navigate back in browser history", "input_schema_json": "{\"type\":\"object\",\"properties\":{},\"required\":[]}", "tool_safety": 0, "original_mcp_server_name": "Playwright", "mcp_server_name": "Playwright", "mcp_tool_name": "playwright_go_back"}, "identifier": {"hostName": "mcpHost", "toolId": "playwright_go_back_Playwright"}, "isConfigured": true, "enabled": false, "toolSafety": 0}, {"definition": {"name": "playwright_go_forward_Playwright", "description": "Navigate forward in browser history", "input_schema_json": "{\"type\":\"object\",\"properties\":{},\"required\":[]}", "tool_safety": 0, "original_mcp_server_name": "Playwright", "mcp_server_name": "Playwright", "mcp_tool_name": "playwright_go_forward"}, "identifier": {"hostName": "mcpHost", "toolId": "playwright_go_forward_Playwright"}, "isConfigured": true, "enabled": false, "toolSafety": 0}, {"definition": {"name": "playwright_drag_Playwright", "description": "Drag an element to a target location", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"sourceSelector\":{\"type\":\"string\",\"description\":\"CSS selector for the element to drag\"},\"targetSelector\":{\"type\":\"string\",\"description\":\"CSS selector for the target location\"}},\"required\":[\"sourceSelector\",\"targetSelector\"]}", "tool_safety": 0, "original_mcp_server_name": "Playwright", "mcp_server_name": "Playwright", "mcp_tool_name": "playwright_drag"}, "identifier": {"hostName": "mcpHost", "toolId": "playwright_drag_Playwright"}, "isConfigured": true, "enabled": false, "toolSafety": 0}, {"definition": {"name": "playwright_press_key_Playwright", "description": "Press a keyboard key", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"key\":{\"type\":\"string\",\"description\":\"Key to press (e.g. 'Enter', 'ArrowDown', 'a')\"},\"selector\":{\"type\":\"string\",\"description\":\"Optional CSS selector to focus before pressing key\"}},\"required\":[\"key\"]}", "tool_safety": 0, "original_mcp_server_name": "Playwright", "mcp_server_name": "Playwright", "mcp_tool_name": "playwright_press_key"}, "identifier": {"hostName": "mcpHost", "toolId": "playwright_press_key_Playwright"}, "isConfigured": true, "enabled": false, "toolSafety": 0}, {"definition": {"name": "playwright_save_as_pdf_Playwright", "description": "Save the current page as a PDF file", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"outputPath\":{\"type\":\"string\",\"description\":\"Directory path where PDF will be saved\"},\"filename\":{\"type\":\"string\",\"description\":\"Name of the PDF file (default: page.pdf)\"},\"format\":{\"type\":\"string\",\"description\":\"Page format (e.g. 'A4', 'Letter')\"},\"printBackground\":{\"type\":\"boolean\",\"description\":\"Whether to print background graphics\"},\"margin\":{\"type\":\"object\",\"description\":\"Page margins\",\"properties\":{\"top\":{\"type\":\"string\"},\"right\":{\"type\":\"string\"},\"bottom\":{\"type\":\"string\"},\"left\":{\"type\":\"string\"}}}},\"required\":[\"outputPath\"]}", "tool_safety": 0, "original_mcp_server_name": "Playwright", "mcp_server_name": "Playwright", "mcp_tool_name": "playwright_save_as_pdf"}, "identifier": {"hostName": "mcpHost", "toolId": "playwright_save_as_pdf_Playwright"}, "isConfigured": true, "enabled": false, "toolSafety": 0}], "disabledTools": [], "type": "stdio", "disabled": false}]