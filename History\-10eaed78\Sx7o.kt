package com.example.surveyassistant.map

import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.Rect
import android.util.Log
import kotlinx.coroutines.*
import org.gdal.gdal.Dataset
import org.gdal.gdal.gdal
import org.gdal.osr.CoordinateTransformation
import org.gdal.osr.SpatialReference
import org.osmdroid.util.BoundingBox
import org.osmdroid.util.GeoPoint
import org.osmdroid.views.MapView
import org.osmdroid.views.Projection
import org.osmdroid.views.overlay.Overlay
import com.example.surveyassistant.utils.CoordinateSystemDetector
import java.io.File
import kotlin.math.*

/**
 * 高性能GeoTIFF栅格图层显示覆盖层
 * 支持坐标系自动识别、动态投影转换和高效渲染
 */
class GeoTiffOverlay(
    private val mapView: MapView,
    private val file: File
) : Overlay() {
    
    companion object {
        private const val TAG = "GeoTiffOverlay"
        private const val MAX_BITMAP_SIZE = 2048
        private const val CACHE_ZOOM_TOLERANCE = 1.0
        
        /**
         * 检查文件是否可以打开
         */
        fun canOpen(file: File): Boolean {
            return try {
                val dataset = gdal.Open(file.absolutePath, 0)
                val result = dataset != null
                dataset?.delete()
                result
            } catch (e: Exception) {
                Log.e(TAG, "检查GeoTIFF文件时出错: ${e.message}", e)
                false
            }
        }
    }
    
    // GDAL数据集
    private var dataset: Dataset? = null
    
    // 地理范围（WGS84坐标系）
    private var boundingBox: BoundingBox? = null
    
    // 图像尺寸
    private var width: Int = 0
    private var height: Int = 0
    
    // 仿射变换参数
    private var geoTransform: DoubleArray? = null
    
    // 坐标转换器
    private var coordTransform: CoordinateTransformation? = null
    private var invCoordTransform: CoordinateTransformation? = null
    
    // 原始投影信息
    private var projectionWkt: String = ""
    private var sourceSRS: SpatialReference? = null
    private var targetSRS: SpatialReference? = null
    
    // 绘制参数
    private val paint = Paint().apply {
        isAntiAlias = true
        isFilterBitmap = true
    }
    
    // 优化的缓存和加载系统
    private val cache = GeoTiffCache.getInstance()
    private val asyncLoader = GeoTiffAsyncLoader.getInstance()
    private val pyramidManager = GeoTiffPyramidManager.getInstance()
    private val distortionCorrector = GeoTiffDistortionCorrector()

    // 无感加载系统
    private val progressiveLoader = GeoTiffProgressiveLoader()
    private val seamlessRenderer = GeoTiffSeamlessRenderer()

    // 缓存管理
    private var cachedBitmap: Bitmap? = null
    private var cachedBoundingBox: BoundingBox? = null
    private var cachedZoomLevel: Double = 0.0
    private var currentCacheKey: String? = null

    // 透明度
    private var alpha: Int = 255

    // 异步加载控制
    private var loadingJob: Job? = null
    private var currentAsyncTaskId: String? = null
    private var isLoading = false
    private val coroutineScope = CoroutineScope(Dispatchers.IO + SupervisorJob())

    // 无感加载优化参数
    private var lastMapMoveTime: Long = 0
    private val REFRESH_DELAY = 50L // 减少到50ms，更快响应
    private var refreshRequested = false

    // 缩放级别优化
    private var lastZoomLevel: Double = 0.0
    private val ZOOM_CHANGE_THRESHOLD = 0.3 // 降低阈值，更敏感
    private var zoomStabilizationTime: Long = 0
    private val ZOOM_STABILIZATION_DELAY = 100L // 减少到100ms，更快稳定

    // 无感加载状态
    private var isSeamlessMode = true
    private var currentLoadTasks = mutableListOf<String>()
    
    init {
        initializeGeoTIFF()
    }
    
    /**
     * 初始化GeoTIFF
     */
    private fun initializeGeoTIFF() {
        coroutineScope.launch {
            try {
                Log.d(TAG, "开始初始化GeoTIFF: ${file.absolutePath}")
                
                // 打开数据集
                dataset = gdal.Open(file.absolutePath, 0)
                if (dataset == null) {
                    Log.e(TAG, "无法打开GeoTIFF文件: ${file.absolutePath}")
                    return@launch
                }
                
                // 获取基本信息
                width = dataset!!.rasterXSize
                height = dataset!!.rasterYSize
                geoTransform = dataset!!.GetGeoTransform()
                projectionWkt = dataset!!.GetProjection() ?: ""
                
                Log.d(TAG, "GeoTIFF基本信息: 尺寸=${width}x${height}")
                Log.d(TAG, "投影信息: $projectionWkt")
                
                // 设置坐标转换
                setupCoordinateTransformation()
                
                // 计算边界框
                calculateBoundingBox()
                
                // 初始化无感加载系统
                if (boundingBox != null && geoTransform != null) {
                    progressiveLoader.initialize(file, dataset!!, boundingBox!!, geoTransform!!)
                    Log.d(TAG, "无感加载系统初始化成功")
                }

                // 在主线程更新UI
                withContext(Dispatchers.Main) {
                    mapView.invalidate()
                }

                Log.d(TAG, "GeoTIFF初始化成功: 边界=${boundingBox}")
                
            } catch (e: Exception) {
                Log.e(TAG, "初始化GeoTIFF失败", e)
            }
        }
    }
    
    /**
     * 设置坐标转换 - 参考mapkancezhushou的智能投影处理
     */
    private fun setupCoordinateTransformation() {
        try {
            // 创建源坐标系
            sourceSRS = SpatialReference()

            // 检查投影是否为CGCS2000坐标系
            val isCGCS2000 = projectionWkt.contains("CGCS2000") || projectionWkt.contains("China_2000")
            var zoneNumber = -1
            var is3DegZone = false
            var is6DegZone = false

            Log.d(TAG, "原始投影WKT: $projectionWkt")
            Log.d(TAG, "检测到CGCS2000: $isCGCS2000")

            // 处理CGCS2000坐标系
            if (isCGCS2000) {
                // 尝试提取带号
                val zoneRegex = "zone (\\d+)".toRegex()
                val matchResult = zoneRegex.find(projectionWkt)
                if (matchResult != null) {
                    zoneNumber = matchResult.groupValues[1].toInt()

                    // 判断是3度带还是6度带
                    is3DegZone = projectionWkt.contains("3-degree") || projectionWkt.contains("3度带")
                    is6DegZone = projectionWkt.contains("6-degree") || projectionWkt.contains("6度带")

                    if (!is3DegZone && !is6DegZone) {
                        // 尝试从中央经线判断
                        if (projectionWkt.contains("central_meridian")) {
                            val meridianRegex = "central_meridian\",([-0-9.]+)".toRegex()
                            val meridianMatch = meridianRegex.find(projectionWkt)
                            if (meridianMatch != null) {
                                val centralMeridian = meridianMatch.groupValues[1].toDouble()
                                // 判断是否为3度带的中央经线
                                is3DegZone = centralMeridian % 3 == 0.0 && centralMeridian % 6 != 0.0
                                // 判断是否为6度带的中央经线
                                is6DegZone = centralMeridian % 6 == 0.0
                            }
                        } else {
                            // 如果无法判断，默认按3度带处理
                            is3DegZone = true
                        }
                    }
                } else {
                    // 尝试从坐标值估计带号
                    val geoTransform = this.geoTransform
                    if (geoTransform != null && geoTransform[0] > 1.0e6) {
                        // 从x坐标估计带号
                        if (geoTransform[0] >= 1.0e7) {
                            // 可能是带号*1000万形式
                            zoneNumber = (geoTransform[0] / 1.0e7).toInt()
                        } else {
                            // 可能是带号*100万形式
                            zoneNumber = (geoTransform[0] / 1.0e6).toInt()
                        }

                        // 判断是3度带还是6度带
                        if (zoneNumber >= 1 && zoneNumber <= 60) {
                            // 中国区域带号范围约13-45
                            // 中国区域一般用3度带，其他区域可能用6度带
                            if (zoneNumber >= 13 && zoneNumber <= 45) {
                                is3DegZone = true
                            } else {
                                is6DegZone = true
                            }
                        }
                    }
                }

                Log.d(TAG, "CGCS2000参数: 带号=$zoneNumber, 3度带=$is3DegZone, 6度带=$is6DegZone")
            }

            // 对于CGCS2000坐标系，构建合适的WKT定义
            if (isCGCS2000 && zoneNumber > 0) {
                setupCGCS2000Projection(zoneNumber, is3DegZone)
            } else {
                setupStandardProjection()
            }

            // 创建目标坐标系(WGS84)
            targetSRS = SpatialReference()
            targetSRS!!.SetWellKnownGeogCS("WGS84")

            // 强制坐标系使用传统轴顺序 (经度,纬度)
            try {
                sourceSRS!!.SetAxisMappingStrategy(0) // OAMS_TRADITIONAL_GIS_ORDER
                targetSRS!!.SetAxisMappingStrategy(0) // OAMS_TRADITIONAL_GIS_ORDER
            } catch (e: Exception) {
                Log.w(TAG, "设置轴映射策略失败: ${e.message}")
            }

            // 创建坐标转换
            coordTransform = CoordinateTransformation(sourceSRS, targetSRS)
            invCoordTransform = CoordinateTransformation(targetSRS, sourceSRS)

            Log.d(TAG, "坐标转换设置成功")

        } catch (e: Exception) {
            Log.e(TAG, "设置坐标转换失败", e)
        }
    }

    /**
     * 设置CGCS2000投影坐标系
     */
    private fun setupCGCS2000Projection(zoneNumber: Int, is3DegZone: Boolean) {
        try {
            // 计算中央经线
            val centralMeridian = if (is3DegZone) {
                (zoneNumber * 3).toDouble()
            } else {
                ((zoneNumber * 6) - 3).toDouble()
            }

            // 构建WKT
            val wkt = if (is3DegZone) {
                """
                PROJCS["CGCS2000 / 3-degree Gauss-Kruger zone $zoneNumber",
                    GEOGCS["CGCS2000",
                        DATUM["China_2000",
                            SPHEROID["CGCS2000",6378137,298.257222101]],
                        PRIMEM["Greenwich",0],
                        UNIT["degree",0.0174532925199433]],
                    PROJECTION["Transverse_Mercator"],
                    PARAMETER["latitude_of_origin",0],
                    PARAMETER["central_meridian",$centralMeridian],
                    PARAMETER["scale_factor",1],
                    PARAMETER["false_easting",${zoneNumber}500000],
                    PARAMETER["false_northing",0],
                    UNIT["metre",1]]
                """.trimIndent()
            } else {
                """
                PROJCS["CGCS2000 / 6-degree Gauss-Kruger zone $zoneNumber",
                    GEOGCS["CGCS2000",
                        DATUM["China_2000",
                            SPHEROID["CGCS2000",6378137,298.257222101]],
                        PRIMEM["Greenwich",0],
                        UNIT["degree",0.0174532925199433]],
                    PROJECTION["Transverse_Mercator"],
                    PARAMETER["latitude_of_origin",0],
                    PARAMETER["central_meridian",$centralMeridian],
                    PARAMETER["scale_factor",1],
                    PARAMETER["false_easting",500000],
                    PARAMETER["false_northing",0],
                    UNIT["metre",1]]
                """.trimIndent()
            }

            val importResult = sourceSRS!!.ImportFromWkt(wkt)
            if (importResult != 0) {
                Log.e(TAG, "导入自定义CGCS2000 WKT失败: $importResult，尝试从原始投影导入")
                sourceSRS!!.ImportFromWkt(projectionWkt)
            } else {
                Log.d(TAG, "成功设置CGCS2000投影: 带号=$zoneNumber, 3度带=$is3DegZone, 中央经线=$centralMeridian")
            }
        } catch (e: Exception) {
            Log.e(TAG, "设置CGCS2000投影失败", e)
            // 回退到原始投影
            sourceSRS!!.ImportFromWkt(projectionWkt)
        }
    }

    /**
     * 设置标准投影坐标系
     */
    private fun setupStandardProjection() {
        try {
            if (projectionWkt.isNotEmpty()) {
                // 尝试直接从WKT导入
                val importResult = sourceSRS!!.ImportFromWkt(projectionWkt)

                if (importResult != 0) {
                    Log.e(TAG, "从WKT导入坐标系失败，尝试简化处理")
                    if (projectionWkt.contains("PROJCS") || projectionWkt.contains("GEOGCS")) {
                        // 尝试从简化的WKT导入
                        try {
                            val simplifiedWkt = simplifyWkt(projectionWkt)
                            sourceSRS!!.ImportFromWkt(simplifiedWkt)
                        } catch (e: Exception) {
                            Log.e(TAG, "处理简化WKT时出错: ${e.message}")
                            // 默认使用WGS84
                            sourceSRS!!.SetWellKnownGeogCS("WGS84")
                        }
                    }
                } else {
                    Log.d(TAG, "成功导入标准投影坐标系")
                }
            } else {
                // 默认使用WGS84
                Log.d(TAG, "未找到投影信息，默认使用WGS84")
                sourceSRS!!.SetWellKnownGeogCS("WGS84")
            }
        } catch (e: Exception) {
            Log.e(TAG, "设置标准投影失败", e)
            sourceSRS!!.SetWellKnownGeogCS("WGS84")
        }
    }

    /**
     * 简化WKT字符串，移除不必要的部分
     */
    private fun simplifyWkt(wkt: String): String {
        return try {
            // 提取WKT中的主要部分
            val regex = "(PROJCS|GEOGCS)\\[\"([^\"]+)\".*".toRegex()
            val matchResult = regex.find(wkt)

            if (matchResult != null) {
                val type = matchResult.groupValues[1]
                val name = matchResult.groupValues[2]

                if (type == "PROJCS") {
                    // 尝试从名称中提取关键信息
                    if (name.contains("WGS") && name.contains("UTM")) {
                        "PROJCS[\"WGS 84 / UTM\",GEOGCS[\"WGS 84\",DATUM[\"WGS_1984\",SPHEROID[\"WGS 84\",6378137,298.257223563]],PRIMEM[\"Greenwich\",0],UNIT[\"degree\",0.0174532925199433]],PROJECTION[\"Transverse_Mercator\"],PARAMETER[\"latitude_of_origin\",0],PARAMETER[\"central_meridian\",0],PARAMETER[\"scale_factor\",0.9996],PARAMETER[\"false_easting\",500000],PARAMETER[\"false_northing\",0],UNIT[\"metre\",1]]"
                    } else {
                        wkt
                    }
                } else {
                    // 对于地理坐标系，尝试简化
                    if (name.contains("WGS") && name.contains("84")) {
                        "GEOGCS[\"WGS 84\",DATUM[\"WGS_1984\",SPHEROID[\"WGS 84\",6378137,298.257223563]],PRIMEM[\"Greenwich\",0],UNIT[\"degree\",0.0174532925199433]]"
                    } else {
                        wkt
                    }
                }
            } else {
                wkt
            }
        } catch (e: Exception) {
            Log.e(TAG, "简化WKT时出错: ${e.message}")
            wkt
        }
    }

    /**
     * 应用坐标系设置（保留原有方法以兼容）
     */
    private fun applyCoordinateSystemSettings(coordInfo: CoordinateSystemDetector.CoordinateSystemInfo) {
        try {
            val settings = CoordinateSystemDetector.getRecommendedSettings(coordInfo)

            // 应用轴序设置
            if (coordInfo.needsAxisFix || settings["axisOrder"] == "traditional") {
                try {
                    sourceSRS?.SetAxisMappingStrategy(org.gdal.osr.osrConstants.OAMS_TRADITIONAL_GIS_ORDER)
                    Log.d(TAG, "应用传统GIS轴序")
                } catch (e: Exception) {
                    Log.w(TAG, "无法设置轴序策略: ${e.message}")
                }
            }

            // 根据坐标系类型应用特定优化
            when (coordInfo.type) {
                CoordinateSystemDetector.CoordinateSystemType.CGCS2000 -> {
                    applyCGCS2000Optimizations()
                }
                CoordinateSystemDetector.CoordinateSystemType.BEIJING54 -> {
                    applyBeijing54Optimizations()
                }
                CoordinateSystemDetector.CoordinateSystemType.XIAN80 -> {
                    applyXian80Optimizations()
                }
                CoordinateSystemDetector.CoordinateSystemType.UTM -> {
                    applyUTMOptimizations(coordInfo)
                }
                else -> {
                    Log.d(TAG, "使用默认坐标系设置")
                }
            }

        } catch (e: Exception) {
            Log.e(TAG, "应用坐标系设置失败", e)
        }
    }
    
    /**
     * 应用CGCS2000坐标系优化
     */
    private fun applyCGCS2000Optimizations() {
        try {
            Log.d(TAG, "应用CGCS2000坐标系优化")
            // CGCS2000与WGS84非常接近，转换精度很高
            // 可以考虑使用高精度转换参数
        } catch (e: Exception) {
            Log.e(TAG, "应用CGCS2000优化失败", e)
        }
    }

    /**
     * 应用北京54坐标系优化
     */
    private fun applyBeijing54Optimizations() {
        try {
            Log.d(TAG, "应用北京54坐标系优化")
            // 北京54坐标系可能需要特殊的转换参数
            // 可以设置更宽松的转换容差
        } catch (e: Exception) {
            Log.e(TAG, "应用北京54优化失败", e)
        }
    }

    /**
     * 应用西安80坐标系优化
     */
    private fun applyXian80Optimizations() {
        try {
            Log.d(TAG, "应用西安80坐标系优化")
            // 西安80坐标系优化
        } catch (e: Exception) {
            Log.e(TAG, "应用西安80优化失败", e)
        }
    }

    /**
     * 应用UTM坐标系优化
     */
    private fun applyUTMOptimizations(coordInfo: CoordinateSystemDetector.CoordinateSystemInfo) {
        try {
            Log.d(TAG, "应用UTM坐标系优化: 带号=${coordInfo.zone}")
            // UTM坐标系通常工作良好，可以使用高精度设置
        } catch (e: Exception) {
            Log.e(TAG, "应用UTM优化失败", e)
        }
    }
    
    /**
     * 记录坐标系信息
     */
    private fun logCoordinateSystemInfo(coordInfo: CoordinateSystemDetector.CoordinateSystemInfo) {
        try {
            val sourceSRS = this.sourceSRS ?: return

            Log.d(TAG, "=== 坐标系详细信息 ===")
            Log.d(TAG, "检测类型: ${coordInfo.type}")
            Log.d(TAG, "坐标系名称: ${coordInfo.name}")
            Log.d(TAG, "描述: ${coordInfo.description}")
            Log.d(TAG, "EPSG代码: ${coordInfo.epsgCode}")
            Log.d(TAG, "需要轴序修复: ${coordInfo.needsAxisFix}")
            coordInfo.zone?.let { Log.d(TAG, "投影带号: $it") }
            coordInfo.centralMeridian?.let { Log.d(TAG, "中央经线: $it") }
            Log.d(TAG, "投影名称: ${sourceSRS.GetAttrValue("PROJECTION")}")
            Log.d(TAG, "椭球体: ${sourceSRS.GetAttrValue("SPHEROID")}")
            Log.d(TAG, "基准面: ${sourceSRS.GetAttrValue("DATUM")}")

        } catch (e: Exception) {
            Log.e(TAG, "记录坐标系信息失败", e)
        }
    }
    
    /**
     * 计算边界框 - 参考mapkancezhushou的精确计算方法
     */
    private fun calculateBoundingBox() {
        try {
            val geoTransform = this.geoTransform ?: return
            val coordTransform = this.coordTransform ?: return

            // 计算四个角点的坐标
            val corners = arrayOf(
                doubleArrayOf(geoTransform[0], geoTransform[3]), // 左上角
                doubleArrayOf(geoTransform[0] + width * geoTransform[1], geoTransform[3] + width * geoTransform[4]), // 右上角
                doubleArrayOf(geoTransform[0] + height * geoTransform[2], geoTransform[3] + height * geoTransform[5]), // 左下角
                doubleArrayOf(geoTransform[0] + width * geoTransform[1] + height * geoTransform[2], geoTransform[3] + width * geoTransform[4] + height * geoTransform[5]) // 右下角
            )

            // 转换到WGS84
            var minLat = 90.0
            var maxLat = -90.0
            var minLon = 180.0
            var maxLon = -180.0

            var validPointCount = 0

            for (corner in corners) {
                val transformed = coordTransform.TransformPoint(corner[0], corner[1], 0.0)
                if (transformed != null && transformed.size == 3) {
                    val lon = transformed[0]
                    val lat = transformed[1]

                    // 检查结果是否合理 (跳过接近0,0的点)
                    if (abs(lon) > 0.0001 || abs(lat) > 0.0001) {
                        minLat = min(minLat, lat)
                        maxLat = max(maxLat, lat)
                        minLon = min(minLon, lon)
                        maxLon = max(maxLon, lon)
                        validPointCount++

                        Log.d(TAG, "角点转换: 原始(${corner[0]}, ${corner[1]}) -> WGS84($lat, $lon)")
                    }
                }
            }

            // 只有当至少有一个有效点时才创建边界框
            if (validPointCount > 0) {
                boundingBox = BoundingBox(maxLat, maxLon, minLat, minLon)
                Log.d(TAG, "边界框计算完成: $boundingBox (有效点数: $validPointCount)")
            } else {
                Log.e(TAG, "无法计算有效的边界框: 所有点都接近(0,0)或转换失败")
                boundingBox = null
            }

        } catch (e: Exception) {
            Log.e(TAG, "计算边界框失败", e)
            boundingBox = null
        }
    }

    /**
     * 设置透明度
     */
    fun setAlpha(alpha: Int) {
        this.alpha = alpha.coerceIn(0, 255)
        paint.alpha = this.alpha
        mapView.invalidate()
    }

    /**
     * 获取边界框
     */
    fun getBoundingBox(): BoundingBox? = boundingBox

    /**
     * 检查是否与当前视图相交
     */
    private fun intersectsWithView(projection: Projection): Boolean {
        val boundingBox = this.boundingBox ?: return false
        val viewBounds = projection.boundingBox
        return boundingBoxIntersects(boundingBox, viewBounds)
    }

    /**
     * 判断两个边界框是否相交
     */
    private fun boundingBoxIntersects(box1: BoundingBox, box2: BoundingBox): Boolean {
        return !(box1.lonEast < box2.lonWest || box1.lonWest > box2.lonEast ||
                box1.latNorth < box2.latSouth || box1.latSouth > box2.latNorth)
    }

    override fun draw(canvas: Canvas?, mapView: MapView?, shadow: Boolean) {
        if (shadow || canvas == null || mapView == null || dataset == null || boundingBox == null) {
            return
        }

        try {
            val projection = mapView.projection

            // 检查是否与当前视图相交
            if (!intersectsWithView(projection)) {
                return
            }

            // 检查是否需要重新生成位图
            val currentZoom = mapView.zoomLevelDouble
            val viewBounds = projection.boundingBox
            val currentTime = System.currentTimeMillis()

            // 智能刷新控制
            val shouldReload = needsRefresh(viewBounds, currentZoom)
            val isRefreshIntervalPassed = currentTime - lastMapMoveTime > REFRESH_DELAY

            // 缩放级别变化检测
            val zoomChanged = abs(currentZoom - lastZoomLevel) > ZOOM_CHANGE_THRESHOLD
            if (zoomChanged) {
                zoomStabilizationTime = currentTime
                lastZoomLevel = currentZoom
            }

            // 检查缩放是否已稳定
            val isZoomStabilized = currentTime - zoomStabilizationTime > ZOOM_STABILIZATION_DELAY

            // 如果需要重新加载且已经过了最小刷新间隔且没有正在加载且缩放已稳定
            if ((shouldReload || refreshRequested) && isRefreshIntervalPassed && !isLoading && isZoomStabilized) {
                refreshRequested = false
                generateBitmapAsync(projection, viewBounds, currentZoom)
            } else if (shouldReload && !refreshRequested) {
                // 标记需要刷新，但等待间隔和缩放稳定
                refreshRequested = true
            }

            // 绘制缓存的位图
            cachedBitmap?.let { bitmap ->
                if (!bitmap.isRecycled) {
                    drawBitmap(canvas, projection, bitmap)
                }
            }

            // 定期清理过期缓存
            if (currentTime % 30000 < 100) { // 大约每30秒清理一次
                cache.cleanupExpiredCache()
            }

        } catch (e: Exception) {
            Log.e(TAG, "绘制GeoTIFF时出错", e)
        }
    }

    /**
     * 检查是否需要刷新
     */
    private fun needsRefresh(viewBounds: BoundingBox, currentZoom: Double): Boolean {
        // 基本检查
        if (cachedBitmap == null || cachedBoundingBox == null) {
            return true
        }

        // 检查边界框是否相交
        if (!boundingBoxIntersects(viewBounds, cachedBoundingBox!!)) {
            return true
        }

        // 检查缩放级别变化
        if (abs(currentZoom - cachedZoomLevel) > CACHE_ZOOM_TOLERANCE) {
            return true
        }

        // 检查视图范围变化是否显著
        val viewAreaChange = calculateAreaChangeRatio(viewBounds, cachedBoundingBox!!)
        if (viewAreaChange > 0.3) { // 如果视图范围变化超过30%
            return true
        }

        return false
    }

    /**
     * 计算两个边界框的面积变化比例
     */
    private fun calculateAreaChangeRatio(box1: BoundingBox, box2: BoundingBox): Double {
        val area1 = (box1.latNorth - box1.latSouth) * (box1.lonEast - box1.lonWest)
        val area2 = (box2.latNorth - box2.latSouth) * (box2.lonEast - box2.lonWest)

        if (area1 == 0.0 || area2 == 0.0) return 1.0

        return abs(area1 - area2) / max(area1, area2)
    }

    /**
     * 异步生成位图 - 使用优化的加载系统
     */
    private fun generateBitmapAsync(projection: Projection, viewBounds: BoundingBox, zoomLevel: Double) {
        // 更新移动时间
        lastMapMoveTime = System.currentTimeMillis()

        // 生成缓存键
        val cacheKey = cache.generateCacheKey(file.absolutePath, viewBounds, zoomLevel)

        // 检查缓存
        val cachedBmp = cache.getBitmap(cacheKey)
        if (cachedBmp != null && !cachedBmp.isRecycled) {
            Log.d(TAG, "从缓存获取位图: $cacheKey")
            updateCachedBitmap(cachedBmp, viewBounds, zoomLevel, cacheKey)
            return
        }

        // 查找最接近的缓存
        val closestCacheKey = cache.findClosestCache(file.absolutePath, viewBounds, zoomLevel)
        if (closestCacheKey != null) {
            val closestBitmap = cache.getBitmap(closestCacheKey)
            val closestMetadata = cache.getCacheMetadata(closestCacheKey)

            if (closestBitmap != null && closestMetadata != null && !closestBitmap.isRecycled) {
                Log.d(TAG, "使用最接近的缓存: $closestCacheKey")
                updateCachedBitmap(closestBitmap, closestMetadata.boundingBox, closestMetadata.zoomLevel, closestCacheKey)

                // 异步加载精确匹配的图像
                loadBitmapAsync(viewBounds, zoomLevel, cacheKey)
                return
            }
        }

        // 异步加载新图像
        loadBitmapAsync(viewBounds, zoomLevel, cacheKey)
    }

    /**
     * 更新缓存的位图
     */
    private fun updateCachedBitmap(bitmap: Bitmap, boundingBox: BoundingBox, zoomLevel: Double, cacheKey: String) {
        // 回收旧位图（如果不在缓存中）
        if (cachedBitmap != null && cachedBitmap != bitmap && currentCacheKey != cacheKey) {
            cachedBitmap?.recycle()
        }

        cachedBitmap = bitmap
        cachedBoundingBox = boundingBox
        cachedZoomLevel = zoomLevel
        currentCacheKey = cacheKey

        // 刷新地图
        mapView.invalidate()
    }

    /**
     * 异步加载位图
     */
    private fun loadBitmapAsync(viewBounds: BoundingBox, zoomLevel: Double, cacheKey: String) {
        if (dataset == null || boundingBox == null || invCoordTransform == null || geoTransform == null) {
            Log.w(TAG, "缺少必要的数据，无法加载")
            return
        }

        // 取消之前的加载任务
        currentAsyncTaskId?.let { asyncLoader.cancelTask(it) }
        isLoading = true

        // 创建回调
        val callback = object : GeoTiffAsyncLoader.LoaderCallback {
            override fun onLoadSuccess(bitmap: Bitmap, boundingBox: BoundingBox, zoomLevel: Double) {
                // 添加到缓存
                cache.addBitmap(cacheKey, bitmap, boundingBox, zoomLevel)

                // 更新当前显示的位图
                updateCachedBitmap(bitmap, boundingBox, zoomLevel, cacheKey)

                isLoading = false
                currentAsyncTaskId = null

                Log.d(TAG, "异步加载成功: $cacheKey")
            }

            override fun onLoadError(error: String) {
                Log.e(TAG, "异步加载失败: $error")
                isLoading = false
                currentAsyncTaskId = null
            }

            override fun onLoadCancelled() {
                Log.d(TAG, "异步加载已取消")
                isLoading = false
                currentAsyncTaskId = null
            }
        }

        // 启动异步加载任务
        currentAsyncTaskId = asyncLoader.loadGeoTIFFAsync(
            file = file,
            dataset = dataset!!,
            bbox = viewBounds,
            geoTransform = geoTransform!!,
            invCoordTransform = invCoordTransform!!,
            boundingBox = boundingBox!!,
            zoomLevel = zoomLevel,
            screenWidth = mapView.width,
            screenHeight = mapView.height,
            maxBitmapSize = MAX_BITMAP_SIZE,
            callback = callback
        )

        Log.d(TAG, "开始异步加载: $currentAsyncTaskId, 缩放级别: $zoomLevel")
    }

    /**
     * 预加载周围区域
     */
    private fun preloadAdjacentAreas(centerBounds: BoundingBox, zoomLevel: Double) {
        if (isLoading) return

        try {
            val latSpan = centerBounds.latNorth - centerBounds.latSouth
            val lonSpan = centerBounds.lonEast - centerBounds.lonWest

            // 预加载8个方向的相邻区域
            val directions = arrayOf(
                Pair(-1.0, 0.0), Pair(1.0, 0.0),   // 左右
                Pair(0.0, -1.0), Pair(0.0, 1.0),   // 上下
                Pair(-1.0, -1.0), Pair(1.0, -1.0), // 对角
                Pair(-1.0, 1.0), Pair(1.0, 1.0)
            )

            for ((lonOffset, latOffset) in directions) {
                val offsetBounds = BoundingBox(
                    centerBounds.latNorth + latSpan * latOffset,
                    centerBounds.lonEast + lonSpan * lonOffset,
                    centerBounds.latSouth + latSpan * latOffset,
                    centerBounds.lonWest + lonSpan * lonOffset
                )

                // 检查是否与GeoTIFF边界相交
                if (boundingBoxIntersects(offsetBounds, boundingBox!!)) {
                    val cacheKey = cache.generateCacheKey(file.absolutePath, offsetBounds, zoomLevel)

                    // 如果缓存中没有，则预加载
                    if (!cache.containsKey(cacheKey)) {
                        // 低优先级预加载（这里简化处理，实际可以实现更复杂的预加载队列）
                        Log.d(TAG, "预加载区域: $cacheKey")
                    }
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "预加载相邻区域失败", e)
        }
    }

    /**
     * 地理坐标转像素坐标（支持动态投影）- 改进版本，解决变形问题
     */
    private fun geoToPixel(bounds: BoundingBox, geoTransform: DoubleArray): Rect? {
        try {
            val invCoordTransform = this.invCoordTransform ?: return null

            // 转换四个角点和中心点，以获得更准确的边界
            val corners = arrayOf(
                Pair(bounds.lonWest, bounds.latNorth),                                  // 左上角
                Pair(bounds.lonEast, bounds.latNorth),                                  // 右上角
                Pair(bounds.lonEast, bounds.latSouth),                                  // 右下角
                Pair(bounds.lonWest, bounds.latSouth),                                  // 左下角
                Pair((bounds.lonWest + bounds.lonEast) / 2, (bounds.latNorth + bounds.latSouth) / 2)  // 中心点
            )

            val pixelCorners = mutableListOf<Pair<Int, Int>>()

            for ((lon, lat) in corners) {
                // WGS84坐标转换到原始投影坐标系
                val projected = invCoordTransform.TransformPoint(lon, lat, 0.0)

                if (projected != null && projected.size >= 2) {
                    val projX = projected[0]
                    val projY = projected[1]

                    // 投影坐标转像素坐标（使用仿射变换的逆变换）
                    val det = geoTransform[1] * geoTransform[5] - geoTransform[2] * geoTransform[4]

                    if (abs(det) < 1e-10) {
                        // 矩阵接近奇异，使用简化版本
                        val pixelX = ((projX - geoTransform[0]) / geoTransform[1]).toInt()
                        val pixelY = ((projY - geoTransform[3]) / geoTransform[5]).toInt()
                        pixelCorners.add(Pair(pixelX, pixelY))
                        Log.d(TAG, "坐标转换(简化): ($lon, $lat) -> ($projX, $projY) -> ($pixelX, $pixelY)")
                    } else {
                        // 使用完整的仿射变换逆矩阵
                        val pixelX = ((geoTransform[5] * (projX - geoTransform[0]) -
                                      geoTransform[2] * (projY - geoTransform[3])) / det).toInt()
                        val pixelY = ((geoTransform[1] * (projY - geoTransform[3]) -
                                      geoTransform[4] * (projX - geoTransform[0])) / det).toInt()

                        pixelCorners.add(Pair(pixelX, pixelY))
                        Log.d(TAG, "坐标转换: ($lon, $lat) -> ($projX, $projY) -> ($pixelX, $pixelY)")
                    }
                } else {
                    Log.w(TAG, "坐标转换失败: ($lon, $lat)")
                }
            }

            if (pixelCorners.size < 4) {
                Log.w(TAG, "有效像素坐标不足: ${pixelCorners.size}/5")
                return null
            }

            // 计算边界
            val minX = pixelCorners.minOf { it.first }
            val maxX = pixelCorners.maxOf { it.first }
            val minY = pixelCorners.minOf { it.second }
            val maxY = pixelCorners.maxOf { it.second }

            // 检查边界是否合理
            val width = maxX - minX
            val height = maxY - minY

            if (width <= 0 || height <= 0) {
                Log.w(TAG, "计算的像素边界无效: width=$width, height=$height")
                return null
            }

            // 检查纵横比是否合理
            val geoAspectRatio = (bounds.lonEast - bounds.lonWest) / (bounds.latNorth - bounds.latSouth)
            val pixelAspectRatio = width.toDouble() / height.toDouble()

            if (abs(geoAspectRatio - pixelAspectRatio) > 0.5) {
                Log.w(TAG, "纵横比异常: 地理=${String.format("%.3f", geoAspectRatio)}, 像素=${String.format("%.3f", pixelAspectRatio)}")
                // 可以在这里进行纵横比修正，但通常不需要，因为我们已经使用了更准确的转换方法
            }

            Log.d(TAG, "地理坐标转像素坐标: 边界($minX,$minY,$maxX,$maxY), 尺寸=${width}x${height}")
            return Rect(minX, minY, maxX, maxY)

        } catch (e: Exception) {
            Log.e(TAG, "地理坐标转像素坐标失败", e)
            return null
        }
    }

    /**
     * 启动预加载任务
     */
    private fun startPreloadTask() {
        if (cachedBoundingBox != null && !isLoading) {
            coroutineScope.launch {
                delay(1000) // 等待1秒后开始预加载
                preloadAdjacentAreas(cachedBoundingBox!!, cachedZoomLevel)
            }
        }
    }

    /**
     * 绘制位图 - 使用变形修正
     */
    private fun drawBitmap(canvas: Canvas, projection: Projection, bitmap: Bitmap) {
        try {
            val boundingBox = this.cachedBoundingBox ?: this.boundingBox ?: return

            // 将地理坐标转换为屏幕坐标
            val topLeft = projection.toPixels(GeoPoint(boundingBox.latNorth, boundingBox.lonWest), null)
            val bottomRight = projection.toPixels(GeoPoint(boundingBox.latSouth, boundingBox.lonEast), null)
            val topRight = projection.toPixels(GeoPoint(boundingBox.latNorth, boundingBox.lonEast), null)
            val bottomLeft = projection.toPixels(GeoPoint(boundingBox.latSouth, boundingBox.lonWest), null)

            // 计算实际的屏幕边界（处理投影变形）
            val minX = minOf(topLeft.x, bottomLeft.x, topRight.x, bottomRight.x)
            val maxX = maxOf(topLeft.x, bottomLeft.x, topRight.x, bottomRight.x)
            val minY = minOf(topLeft.y, bottomLeft.y, topRight.y, bottomRight.y)
            val maxY = maxOf(topLeft.y, bottomLeft.y, topRight.y, bottomRight.y)

            val destRect = Rect(minX, minY, maxX, maxY)

            // 检查目标矩形是否有效
            if (destRect.width() <= 0 || destRect.height() <= 0) {
                Log.w(TAG, "目标矩形无效: $destRect")
                return
            }

            // 检测变形
            val distortionInfo = distortionCorrector.detectDistortion(bitmap, boundingBox, destRect)

            if (distortionInfo.hasDistortion) {
                Log.d(TAG, "检测到变形，应用修正: ${distortionInfo.recommendedCorrection}")

                // 计算地理纵横比
                val geoWidth = boundingBox.lonEast - boundingBox.lonWest
                val geoHeight = boundingBox.latNorth - boundingBox.latSouth
                val geoAspectRatio = abs(geoWidth / geoHeight).toFloat()

                // 修正目标矩形
                val correctedDestRect = distortionCorrector.correctScreenBounds(
                    destRect, distortionInfo, geoAspectRatio
                )

                // 使用修正后的绘制方法
                val sourceBounds = Rect(0, 0, bitmap.width, bitmap.height)
                distortionCorrector.drawCorrectedBitmap(
                    canvas, bitmap, sourceBounds, correctedDestRect, distortionInfo, paint
                )

                // 调试信息
                if (Log.isLoggable(TAG, Log.DEBUG)) {
                    Log.d(TAG, "变形修正: 原始=$destRect, 修正=$correctedDestRect")
                    Log.d(TAG, distortionCorrector.getDistortionReport(distortionInfo))
                }
            } else {
                // 无变形，直接绘制
                canvas.drawBitmap(bitmap, null, destRect, paint)

                if (Log.isLoggable(TAG, Log.DEBUG)) {
                    Log.d(TAG, "无变形，直接绘制: ${bitmap.width}x${bitmap.height} -> $destRect")
                }
            }

        } catch (e: Exception) {
            Log.e(TAG, "绘制位图时出错", e)
            // 回退到简单绘制
            try {
                val boundingBox = this.cachedBoundingBox ?: this.boundingBox ?: return
                val topLeft = projection.toPixels(GeoPoint(boundingBox.latNorth, boundingBox.lonWest), null)
                val bottomRight = projection.toPixels(GeoPoint(boundingBox.latSouth, boundingBox.lonEast), null)
                val destRect = Rect(topLeft.x, topLeft.y, bottomRight.x, bottomRight.y)
                canvas.drawBitmap(bitmap, null, destRect, paint)
            } catch (fallbackError: Exception) {
                Log.e(TAG, "回退绘制也失败", fallbackError)
            }
        }
    }

    /**
     * 调整矩形以保持正确的纵横比
     */
    private fun adjustRectForAspectRatio(rect: Rect, targetAspectRatio: Float): Rect {
        val currentAspectRatio = rect.width().toFloat() / rect.height().toFloat()

        return if (targetAspectRatio > currentAspectRatio) {
            // 需要增加宽度
            val newWidth = (rect.height() * targetAspectRatio).toInt()
            val widthDiff = newWidth - rect.width()
            Rect(
                rect.left - widthDiff / 2,
                rect.top,
                rect.right + widthDiff / 2,
                rect.bottom
            )
        } else {
            // 需要增加高度
            val newHeight = (rect.width() / targetAspectRatio).toInt()
            val heightDiff = newHeight - rect.height()
            Rect(
                rect.left,
                rect.top - heightDiff / 2,
                rect.right,
                rect.bottom + heightDiff / 2
            )
        }
    }

    /**
     * 清理资源
     */
    fun cleanup() {
        try {
            // 取消异步任务
            loadingJob?.cancel()
            currentAsyncTaskId?.let { asyncLoader.cancelTask(it) }
            coroutineScope.cancel()

            // 清理位图（不回收缓存中的位图）
            if (cachedBitmap != null && currentCacheKey != null) {
                // 如果位图在缓存中，不要回收
                if (!cache.containsKey(currentCacheKey!!)) {
                    cachedBitmap?.recycle()
                }
            }
            cachedBitmap = null
            currentCacheKey = null

            // 清理GDAL资源
            dataset?.delete()
            dataset = null
            coordTransform?.delete()
            coordTransform = null
            invCoordTransform?.delete()
            invCoordTransform = null
            sourceSRS?.delete()
            sourceSRS = null
            targetSRS?.delete()
            targetSRS = null

            Log.d(TAG, "GeoTIFF资源已清理")
        } catch (e: Exception) {
            Log.e(TAG, "清理资源时出错", e)
        }
    }

    /**
     * 获取优化状态信息
     */
    fun getOptimizationInfo(): String {
        return try {
            val pyramidInfo = if (dataset != null) {
                pyramidManager.getPyramidStatusInfo(file)
            } else {
                "数据集未初始化"
            }

            val cacheInfo = cache.getCacheStats()

            buildString {
                appendLine("=== GeoTIFF优化状态 ===")
                appendLine("文件: ${file.name}")
                appendLine("当前状态: ${if (isLoading) "加载中" else "就绪"}")
                appendLine("缓存键: $currentCacheKey")
                appendLine()
                appendLine(pyramidInfo)
                appendLine()
                appendLine(cacheInfo)
            }
        } catch (e: Exception) {
            "获取优化信息失败: ${e.message}"
        }
    }
}
