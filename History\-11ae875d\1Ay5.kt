package com.example.surveyassistant.ui.theme

import androidx.compose.ui.graphics.Color

// 新拟态蓝白配色方案
// 主背景色 - 浅蓝灰色调
val NeumorphismBackground = Color(0xFFE8F4FD)    // 主背景色

// 蓝色系 - 新拟态风格
val NeumorphismBlue = Color(0xFF2196F3)          // 主蓝色
val NeumorphismBlueLight = Color(0xFF64B5F6)     // 浅蓝色
val NeumorphismBlueDark = Color(0xFF1976D2)      // 深蓝色
val NeumorphismBlueVeryLight = Color(0xFFBBDEFB) // 极浅蓝色

// 白色系 - 新拟态风格
val NeumorphismWhite = Color(0xFFFFFFFF)         // 纯白色
val NeumorphismOffWhite = Color(0xFFFAFCFF)      // 微蓝白色
val NeumorphismLightGray = Color(0xFFF0F7FF)     // 浅蓝灰色

// 阴影色彩 - 新拟态效果
val NeumorphismShadowLight = Color(0xFFFFFFFF)   // 浅色阴影（高光）
val NeumorphismShadowDark = Color(0xFFD1E9FF)    // 深色阴影
val NeumorphismShadowMedium = Color(0xFFE0F2FF)  // 中等阴影

// 文字色彩
val NeumorphismTextPrimary = Color(0xFF1565C0)   // 主要文字色
val NeumorphismTextSecondary = Color(0xFF42A5F5) // 次要文字色
val NeumorphismTextHint = Color(0xFF90CAF9)      // 提示文字色

// 功能色彩 - 保持原有功能色
val NeumorphismSuccess = Color(0xFF4CAF50)       // 成功色
val NeumorphismError = Color(0xFFF44336)         // 错误色
val NeumorphismWarning = Color(0xFFFF9800)       // 警告色
val NeumorphismInfo = Color(0xFF2196F3)          // 信息色

// 按钮和交互元素
val NeumorphismButtonBackground = Color(0xFFE8F4FD) // 按钮背景
val NeumorphismButtonPressed = Color(0xFFD1E9FF)    // 按钮按下状态
val NeumorphismButtonDisabled = Color(0xFFF0F7FF)   // 按钮禁用状态