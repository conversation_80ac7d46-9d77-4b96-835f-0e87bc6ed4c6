using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using ArcGIS.Core.Data;
using ArcGIS.Core.Geometry;
using ArcGIS.Desktop.Mapping;
using ArcGIS.Desktop.Framework.Threading.Tasks;
using XIAOFUTools.Tools.AIAssistant.Agent.Tools.Core;
using XIAOFUTools.Tools.AIAssistant.Agent.Tools.Models;

namespace XIAOFUTools.Tools.AIAssistant.Agent.Tools.Analysis
{
    /// <summary>
    /// 后台面积计算工具
    /// </summary>
    public class AreaCalculationTool : BaseBatchTool
    {
        public override string Name => "AreaCalculation";
        public override string Description => "后台计算图层要素的面积、周长等几何属性，支持不同单位和投影，支持批量计算多个图层";
        public override ToolCategory Category => ToolCategory.SpatialAnalysis;

        private static readonly IReadOnlyList<ToolParameter> _parameters = new List<ToolParameter>
        {
            CreateParameter("layer_names", "要计算面积的图层名称列表", typeof(List<string>), true),
            CreateParameter("calculation_types", "计算类型", typeof(List<string>), false, new List<string> { "area" }),
            CreateParameter("area_units", "面积单位", typeof(string), false, "square_meters", "square_meters", "square_kilometers", "hectares", "acres", "square_feet"),
            CreateParameter("length_units", "长度单位", typeof(string), false, "meters", "meters", "kilometers", "feet", "miles"),
            CreateParameter("use_geodesic", "是否使用测地线计算（更精确）", typeof(bool), false, true),
            CreateParameter("include_statistics", "是否包含统计信息（总面积、平均面积等）", typeof(bool), false, true),
            CreateParameter("group_by_field", "按指定字段分组计算统计信息", typeof(string), false),
            CreateParameter("where_clause", "SQL查询条件，用于筛选要素", typeof(string), false),
            CreateParameter("output_format", "输出格式", typeof(string), false, "summary", "summary", "detailed", "features")
        }.AsReadOnly();

        public override IReadOnlyList<ToolParameter> Parameters => _parameters;

        protected override async Task<ToolResult> ExecuteInternalAsync(
            Dictionary<string, object> parameters,
            AgentContext context,
            CancellationToken cancellationToken)
        {
            try
            {
                var layerNames = GetRequiredParameter<List<string>>(parameters, "layer_names");
                var calculationTypes = GetOptionalParameter<List<string>>(parameters, "calculation_types", new List<string> { "area" });
                var areaUnits = GetOptionalParameter<string>(parameters, "area_units", "square_meters");
                var lengthUnits = GetOptionalParameter<string>(parameters, "length_units", "meters");
                var useGeodesic = GetOptionalParameter<bool>(parameters, "use_geodesic", true);
                var includeStatistics = GetOptionalParameter<bool>(parameters, "include_statistics", true);
                var groupByField = GetOptionalParameter<string>(parameters, "group_by_field", "");
                var whereClause = GetOptionalParameter<string>(parameters, "where_clause", "");
                var outputFormat = GetOptionalParameter<string>(parameters, "output_format", "summary");

                if (layerNames == null || layerNames.Count == 0)
                {
                    return ToolResult.Failure("必须指定至少一个图层名称");
                }

                var result = await QueuedTask.Run(() =>
                {
                    var calculationResults = new List<object>();
                    var map = MapView.Active?.Map;
                    
                    if (map == null)
                    {
                        return new { error = "没有活动地图" };
                    }

                    foreach (var layerName in layerNames)
                    {
                        var layer = map.GetLayersAsFlattenedList().OfType<FeatureLayer>()
                            .FirstOrDefault(l => l.Name.Equals(layerName, StringComparison.OrdinalIgnoreCase));
                        
                        if (layer != null)
                        {
                            var layerResult = CalculateLayerAreas(layer, calculationTypes, areaUnits, lengthUnits,
                                useGeodesic, includeStatistics, groupByField, whereClause, outputFormat);
                            calculationResults.Add(layerResult);
                        }
                        else
                        {
                            calculationResults.Add(new { layer_name = layerName, error = "图层未找到" });
                        }
                    }

                    return new
                    {
                        calculation_types = calculationTypes,
                        area_units = areaUnits,
                        length_units = lengthUnits,
                        use_geodesic = useGeodesic,
                        total_layers = layerNames.Count,
                        successful_calculations = calculationResults.Count(r => !((dynamic)r).GetType().GetProperty("error")?.GetValue(r, null) != null),
                        results = calculationResults,
                        timestamp = DateTime.Now
                    };
                });

                return ToolResult.Success(result);
            }
            catch (Exception ex)
            {
                return ToolResult.Failure($"面积计算失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 计算图层面积
        /// </summary>
        private object CalculateLayerAreas(FeatureLayer layer, List<string> calculationTypes,
            string areaUnits, string lengthUnits, bool useGeodesic, bool includeStatistics,
            string groupByField, string whereClause, string outputFormat)
        {
            try
            {
                var table = layer.GetTable();
                var definition = table.GetDefinition();
                var geometryType = definition.GetShapeType();

                // 检查几何类型
                if (geometryType != GeometryType.Polygon && geometryType != GeometryType.Polyline)
                {
                    return new { layer_name = layer.Name, error = "图层必须是面要素或线要素类型" };
                }

                var queryFilter = new QueryFilter();
                if (!string.IsNullOrEmpty(whereClause))
                {
                    queryFilter.WhereClause = whereClause;
                }

                var featureCalculations = new List<object>();
                var groupedCalculations = new Dictionary<object, List<double>>();
                var allAreas = new List<double>();
                var allPerimeters = new List<double>();
                var allLengths = new List<double>();

                // 获取面积和长度单位
                var areaUnit = GetAreaUnit(areaUnits);
                var lengthUnit = GetLengthUnit(lengthUnits);

                using (var cursor = table.Search(queryFilter, false))
                {
                    while (cursor.MoveNext())
                    {
                        var feature = cursor.Current as Feature;
                        if (feature == null) continue;

                        var geometry = feature.GetShape();
                        if (geometry == null) continue;

                        var featureCalc = new Dictionary<string, object>
                        {
                            ["feature_id"] = feature.GetObjectID()
                        };

                        // 计算面积
                        if (calculationTypes.Contains("area") || calculationTypes.Contains("both"))
                        {
                            if (geometryType == GeometryType.Polygon)
                            {
                                double area = useGeodesic ? 
                                    GeometryEngine.Instance.GeodesicArea(geometry as Polygon, areaUnit) :
                                    GeometryEngine.Instance.Area(geometry, areaUnit);
                                
                                featureCalc["area"] = Math.Abs(area); // 取绝对值，避免负面积
                                allAreas.Add(Math.Abs(area));
                            }
                        }

                        // 计算周长或长度
                        if (calculationTypes.Contains("perimeter") || calculationTypes.Contains("both") || calculationTypes.Contains("length"))
                        {
                            double length = useGeodesic ?
                                GeometryEngine.Instance.GeodesicLength(geometry, lengthUnit) :
                                GeometryEngine.Instance.Length(geometry, lengthUnit);
                            
                            if (geometryType == GeometryType.Polygon)
                            {
                                featureCalc["perimeter"] = length;
                                allPerimeters.Add(length);
                            }
                            else
                            {
                                featureCalc["length"] = length;
                                allLengths.Add(length);
                            }
                        }

                        // 分组统计
                        if (!string.IsNullOrEmpty(groupByField))
                        {
                            var groupValue = feature[groupByField] ?? "NULL";
                            if (!groupedCalculations.ContainsKey(groupValue))
                            {
                                groupedCalculations[groupValue] = new List<double>();
                            }
                            
                            if (featureCalc.ContainsKey("area"))
                            {
                                groupedCalculations[groupValue].Add((double)featureCalc["area"]);
                            }
                        }

                        if (outputFormat == "features" || outputFormat == "detailed")
                        {
                            featureCalculations.Add(featureCalc);
                        }
                    }
                }

                var result = new Dictionary<string, object>
                {
                    ["layer_name"] = layer.Name,
                    ["geometry_type"] = geometryType.ToString(),
                    ["calculation_types"] = calculationTypes,
                    ["area_units"] = areaUnits,
                    ["length_units"] = lengthUnits,
                    ["use_geodesic"] = useGeodesic,
                    ["total_features"] = featureCalculations.Count
                };

                // 添加统计信息
                if (includeStatistics)
                {
                    var statistics = new Dictionary<string, object>();

                    if (allAreas.Count > 0)
                    {
                        statistics["area_statistics"] = new
                        {
                            total_area = allAreas.Sum(),
                            average_area = allAreas.Average(),
                            min_area = allAreas.Min(),
                            max_area = allAreas.Max(),
                            standard_deviation = CalculateStandardDeviation(allAreas),
                            feature_count = allAreas.Count
                        };
                    }

                    if (allPerimeters.Count > 0)
                    {
                        statistics["perimeter_statistics"] = new
                        {
                            total_perimeter = allPerimeters.Sum(),
                            average_perimeter = allPerimeters.Average(),
                            min_perimeter = allPerimeters.Min(),
                            max_perimeter = allPerimeters.Max(),
                            standard_deviation = CalculateStandardDeviation(allPerimeters)
                        };
                    }

                    if (allLengths.Count > 0)
                    {
                        statistics["length_statistics"] = new
                        {
                            total_length = allLengths.Sum(),
                            average_length = allLengths.Average(),
                            min_length = allLengths.Min(),
                            max_length = allLengths.Max(),
                            standard_deviation = CalculateStandardDeviation(allLengths)
                        };
                    }

                    result["statistics"] = statistics;
                }

                // 添加分组统计
                if (!string.IsNullOrEmpty(groupByField) && groupedCalculations.Count > 0)
                {
                    var groupStats = groupedCalculations.Select(g => new
                    {
                        group_value = g.Key,
                        feature_count = g.Value.Count,
                        total_area = g.Value.Sum(),
                        average_area = g.Value.Average(),
                        min_area = g.Value.Min(),
                        max_area = g.Value.Max()
                    }).ToList();

                    result["grouped_statistics"] = groupStats;
                }

                // 添加要素级别的计算结果
                if (outputFormat == "features" || outputFormat == "detailed")
                {
                    result["feature_calculations"] = featureCalculations;
                }

                return result;
            }
            catch (Exception ex)
            {
                return new { layer_name = layer.Name, error = $"计算失败: {ex.Message}" };
            }
        }

        /// <summary>
        /// 获取面积单位
        /// </summary>
        private AreaUnit GetAreaUnit(string unitName)
        {
            return unitName.ToLower() switch
            {
                "square_meters" => AreaUnit.SquareMeters,
                "square_kilometers" => AreaUnit.SquareKilometers,
                "hectares" => AreaUnit.Hectares,
                "acres" => AreaUnit.Acres,
                "square_feet" => AreaUnit.SquareFeet,
                _ => AreaUnit.SquareMeters
            };
        }

        /// <summary>
        /// 获取长度单位
        /// </summary>
        private LinearUnit GetLengthUnit(string unitName)
        {
            return unitName.ToLower() switch
            {
                "meters" => LinearUnit.Meters,
                "kilometers" => LinearUnit.Kilometers,
                "feet" => LinearUnit.Feet,
                "miles" => LinearUnit.Miles,
                _ => LinearUnit.Meters
            };
        }

        /// <summary>
        /// 计算标准差
        /// </summary>
        private double CalculateStandardDeviation(List<double> values)
        {
            if (values.Count <= 1) return 0;

            var average = values.Average();
            var sumOfSquares = values.Sum(v => Math.Pow(v - average, 2));
            return Math.Sqrt(sumOfSquares / (values.Count - 1));
        }
    }
}
