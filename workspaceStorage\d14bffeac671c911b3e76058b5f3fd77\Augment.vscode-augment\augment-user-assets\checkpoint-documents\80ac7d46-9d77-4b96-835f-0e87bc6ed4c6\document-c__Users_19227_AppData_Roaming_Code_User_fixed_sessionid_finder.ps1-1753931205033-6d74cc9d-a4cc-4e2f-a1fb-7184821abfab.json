{"path": {"rootPath": "c:\\Users\\<USER>\\AppData\\Roaming\\Code\\User", "relPath": "fixed_sessionid_finder.ps1"}, "modifiedCode": "# Fixed SessionId Finder\nWrite-Host \"=== Fixed VS Code SessionId Scanner ===\" -ForegroundColor Green\n\n$script:sessionIds = @()\n$uuidPattern = '[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}'\n\nfunction Add-SessionId {\n    param($Type, $Location, $SessionId, $Source = \"\")\n    $script:sessionIds += [PSCustomObject]@{\n        Type = $Type\n        Location = $Location\n        SessionId = $SessionId\n        Source = $Source\n    }\n}\n\nfunction Search-FileForUUIDs {\n    param($FilePath, $Type, $RelativePath)\n    try {\n        $content = Get-Content $FilePath -Raw -ErrorAction SilentlyContinue\n        if ($content) {\n            $matches = [regex]::Matches($content, $uuidPattern)\n            foreach ($match in $matches) {\n                Add-SessionId -Type $Type -Location $RelativePath -SessionId $match.Value -Source \"File content\"\n                Write-Host \"  Found UUID: $($match.Value)\" -ForegroundColor Green\n            }\n        }\n    }\n    catch {\n        # Silently ignore errors for binary files\n    }\n}\n\n# 1. Check telemetry sessionId in globalStorage\nWrite-Host \"`n[1] Checking global storage...\" -ForegroundColor Cyan\n$globalPath = \"globalStorage\\storage.json\"\nif (Test-Path $globalPath) {\n    $global = Get-Content $globalPath | ConvertFrom-Json\n    if ($global.'telemetry.sessionId') {\n        Add-SessionId -Type \"Telemetry\" -Location $globalPath -SessionId $global.'telemetry.sessionId' -Source \"telemetry.sessionId\"\n        Write-Host \"Telemetry SessionId: $($global.'telemetry.sessionId')\" -ForegroundColor Green\n    }\n    \n    # Check for other sessionIds in the same file\n    $globalContent = Get-Content $globalPath -Raw\n    $matches = [regex]::Matches($globalContent, $uuidPattern)\n    foreach ($match in $matches) {\n        if ($match.Value -ne $global.'telemetry.sessionId') {\n            Add-SessionId -Type \"GlobalStorage\" -Location $globalPath -SessionId $match.Value -Source \"Other UUID in storage.json\"\n            Write-Host \"Other UUID in storage.json: $($match.Value)\" -ForegroundColor Yellow\n        }\n    }\n}\n\n# 2. Check workspaceStorage directories\nWrite-Host \"`n[2] Checking workspace storage...\" -ForegroundColor Cyan\n$workspaces = Get-ChildItem \"workspaceStorage\" -Directory\n\nforeach ($workspace in $workspaces) {\n    Write-Host \"Workspace: $($workspace.Name)\" -ForegroundColor Yellow\n    \n    # Check chatSessions\n    $chatPath = Join-Path $workspace.FullName \"chatSessions\"\n    if (Test-Path $chatPath) {\n        $chatFiles = Get-ChildItem $chatPath -Filter \"*.json\"\n        foreach ($file in $chatFiles) {\n            try {\n                $chat = Get-Content $file.FullName | ConvertFrom-Json\n                if ($chat.sessionId) {\n                    Add-SessionId -Type \"ChatSession\" -Location \"workspaceStorage\\$($workspace.Name)\\chatSessions\\$($file.Name)\" -SessionId $chat.sessionId -Source \"chatSessions JSON\"\n                    Write-Host \"  Chat SessionId: $($chat.sessionId)\" -ForegroundColor Green\n                }\n            }\n            catch {\n                Write-Host \"  Error reading $($file.Name)\" -ForegroundColor Red\n            }\n        }\n    }\n    \n    # Check chatEditingSessions\n    $editPath = Join-Path $workspace.FullName \"chatEditingSessions\"\n    if (Test-Path $editPath) {\n        $editFiles = Get-ChildItem $editPath -Filter \"*.json\"\n        foreach ($file in $editFiles) {\n            try {\n                $edit = Get-Content $file.FullName | ConvertFrom-Json\n                if ($edit.sessionId) {\n                    Add-SessionId -Type \"ChatEditingSession\" -Location \"workspaceStorage\\$($workspace.Name)\\chatEditingSessions\\$($file.Name)\" -SessionId $edit.sessionId -Source \"chatEditingSessions JSON\"\n                    Write-Host \"  Edit SessionId: $($edit.sessionId)\" -ForegroundColor Green\n                }\n            }\n            catch {\n                Write-Host \"  Error reading $($file.Name)\" -ForegroundColor Red\n            }\n        }\n    }\n}\n\n# 3. Check emptyWindowChatSessions\nWrite-Host \"`n[3] Checking empty window chat sessions...\" -ForegroundColor Cyan\n$emptyWindowPath = \"globalStorage\\emptyWindowChatSessions\"\nif (Test-Path $emptyWindowPath) {\n    $emptyFiles = Get-ChildItem $emptyWindowPath -Filter \"*.json\" -ErrorAction SilentlyContinue\n    foreach ($file in $emptyFiles) {\n        try {\n            $emptyChat = Get-Content $file.FullName | ConvertFrom-Json\n            if ($emptyChat.sessionId) {\n                Add-SessionId -Type \"EmptyWindowChat\" -Location \"globalStorage\\emptyWindowChatSessions\\$($file.Name)\" -SessionId $emptyChat.sessionId -Source \"emptyWindowChatSessions JSON\"\n                Write-Host \"Empty Window Chat SessionId: $($emptyChat.sessionId)\" -ForegroundColor Green\n            }\n        }\n        catch {\n            Write-Host \"  Error reading $($file.Name)\" -ForegroundColor Red\n        }\n    }\n}\n\n# Output results\nWrite-Host \"`n=== SCAN RESULTS ===\" -ForegroundColor Green\nWrite-Host \"Total SessionIds found: $($script:sessionIds.Count)\" -ForegroundColor Yellow\n\nif ($script:sessionIds.Count -gt 0) {\n    # Group by type\n    $grouped = $script:sessionIds | Group-Object Type\n    foreach ($group in $grouped) {\n        Write-Host \"$($group.Name): $($group.Count) items\" -ForegroundColor Cyan\n    }\n    \n    # Show unique sessionIds\n    $uniqueIds = $script:sessionIds | Select-Object SessionId -Unique\n    Write-Host \"`nUnique SessionIds: $($uniqueIds.Count)\" -ForegroundColor Yellow\n    \n    # Save detailed results\n    $script:sessionIds | Export-Csv \"sessionids_detailed.csv\" -NoTypeInformation -Encoding UTF8\n    $script:sessionIds | Format-Table -AutoSize | Out-File \"sessionids_report.txt\" -Encoding UTF8\n    \n    Write-Host \"`nDetailed results saved to:\" -ForegroundColor Green\n    Write-Host \"- sessionids_detailed.csv\" -ForegroundColor Cyan\n    Write-Host \"- sessionids_report.txt\" -ForegroundColor Cyan\n    \n    # Display all results\n    Write-Host \"`n=== All SessionIds ===\" -ForegroundColor Green\n    $script:sessionIds | Format-Table Type, SessionId, Source -AutoSize\n}\n\nWrite-Host \"`nScan completed!\" -ForegroundColor Green\n"}