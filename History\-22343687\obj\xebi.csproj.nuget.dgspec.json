{"format": 1, "restore": {"C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\History\\-22343687\\xebi.csproj": {}}, "projects": {"C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\History\\-22343687\\xebi.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\History\\-22343687\\xebi.csproj", "projectName": "xebi", "projectPath": "C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\History\\-22343687\\xebi.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\History\\-22343687\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\RUANJIAN\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "dependencies": {"DuckDB.NET.Data.Full": {"target": "Package", "version": "[1.2.0, )"}, "Microsoft.Web.WebView2": {"target": "Package", "version": "[1.0.3296.44, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "System.Management": {"target": "Package", "version": "[8.0.0, )"}, "System.Text.Encoding.CodePages": {"target": "Package", "version": "[8.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}, "runtimes": {"win-x64": {"#import": []}}}}}