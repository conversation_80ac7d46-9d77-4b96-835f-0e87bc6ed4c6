package com.example.surveyassistant.ui.theme

import androidx.compose.ui.graphics.Color

// 蓝白配色主题 - 浅色主题
val Blue50 = Color(0xFFE3F2FD)
val Blue100 = Color(0xFFBBDEFB)
val Blue200 = Color(0xFF90CAF9)
val Blue300 = Color(0xFF64B5F6)
val Blue400 = Color(0xFF42A5F5)
val Blue500 = Color(0xFF2196F3)
val Blue600 = Color(0xFF1E88E5)
val Blue700 = Color(0xFF1976D2)
val Blue800 = Color(0xFF1565C0)
val Blue900 = Color(0xFF0D47A1)

// 蓝色变体
val LightBlue50 = Color(0xFFE1F5FE)
val LightBlue100 = Color(0xFFB3E5FC)
val LightBlue200 = Color(0xFF81D4FA)
val LightBlue300 = Color(0xFF4FC3F7)
val LightBlue400 = Color(0xFF29B6F6)
val LightBlue500 = Color(0xFF03A9F4)
val LightBlue600 = Color(0xFF039BE5)
val LightBlue700 = Color(0xFF0288D1)
val LightBlue800 = Color(0xFF0277BD)
val LightBlue900 = Color(0xFF01579B)

// 深蓝色
val DarkBlue50 = Color(0xFFE8EAF6)
val DarkBlue100 = Color(0xFFC5CAE9)
val DarkBlue200 = Color(0xFF9FA8DA)
val DarkBlue300 = Color(0xFF7986CB)
val DarkBlue400 = Color(0xFF5C6BC0)
val DarkBlue500 = Color(0xFF3F51B5)
val DarkBlue600 = Color(0xFF3949AB)
val DarkBlue700 = Color(0xFF303F9F)
val DarkBlue800 = Color(0xFF283593)
val DarkBlue900 = Color(0xFF1A237E)

// 灰色系
val Grey50 = Color(0xFFFAFAFA)
val Grey100 = Color(0xFFF5F5F5)
val Grey200 = Color(0xFFEEEEEE)
val Grey300 = Color(0xFFE0E0E0)
val Grey400 = Color(0xFFBDBDBD)
val Grey500 = Color(0xFF9E9E9E)
val Grey600 = Color(0xFF757575)
val Grey700 = Color(0xFF616161)
val Grey800 = Color(0xFF424242)
val Grey900 = Color(0xFF212121)

// 白色系
val White = Color(0xFFFFFFFF)
val OffWhite = Color(0xFFFCFCFC)
val LightGrey = Color(0xFFF8F9FA)

// 功能色彩
val Success = Color(0xFF4CAF50)
val Warning = Color(0xFFFF9800)
val Error = Color(0xFFF44336)
val Info = Color(0xFF2196F3)

// 地图相关颜色
val MapPrimary = Blue600
val MapSecondary = LightBlue500
val MapAccent = Blue400
val MapBackground = Grey50
val MapSurface = White

// 测量工具颜色
val MeasureDistance = Color(0xFFE91E63)
val MeasureArea = Color(0xFF9C27B0)
val MeasureBearing = Color(0xFF673AB7)
val MeasureCoordinate = Color(0xFF3F51B5)

// 标记颜色
val MarkerPoint = Color(0xFFF44336)
val MarkerLine = Color(0xFF2196F3)
val MarkerPolygon = Color(0xFF4CAF50)
val MarkerText = Color(0xFFFF9800)