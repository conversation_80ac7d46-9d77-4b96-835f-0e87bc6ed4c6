# Database SessionId Extractor
Write-Host "=== SQLite Database SessionId Extractor ===" -ForegroundColor Green

# Check if sqlite3 is available
$sqlite3Path = $null
$possiblePaths = @(
    "sqlite3.exe",
    "C:\Program Files\SQLite\sqlite3.exe",
    "C:\sqlite\sqlite3.exe",
    "$env:USERPROFILE\sqlite3.exe"
)

foreach ($path in $possiblePaths) {
    try {
        $result = & $path -version 2>$null
        if ($LASTEXITCODE -eq 0) {
            $sqlite3Path = $path
            break
        }
    }
    catch {
        # Continue searching
    }
}

if (-not $sqlite3Path) {
    Write-Host "SQLite3 not found. Attempting to download..." -ForegroundColor Yellow
    
    # Try to download sqlite3
    try {
        $downloadUrl = "https://www.sqlite.org/2023/sqlite-tools-win32-x86-3420000.zip"
        $zipPath = "$env:TEMP\sqlite-tools.zip"
        $extractPath = "$env:TEMP\sqlite-tools"
        
        Write-Host "Downloading SQLite tools..." -ForegroundColor Cyan
        Invoke-WebRequest -Uri $downloadUrl -OutFile $zipPath -UseBasicParsing
        
        Write-Host "Extracting SQLite tools..." -ForegroundColor Cyan
        Expand-Archive -Path $zipPath -DestinationPath $extractPath -Force
        
        $sqlite3Path = Get-ChildItem -Path $extractPath -Name "sqlite3.exe" -Recurse | Select-Object -First 1
        if ($sqlite3Path) {
            $sqlite3Path = Join-Path $extractPath $sqlite3Path.FullName
            Write-Host "SQLite3 downloaded successfully!" -ForegroundColor Green
        }
    }
    catch {
        Write-Host "Failed to download SQLite3. Using alternative method..." -ForegroundColor Red
    }
}

function Query-SQLiteDatabase {
    param($DatabasePath, $DatabaseName)
    
    Write-Host "`nAnalyzing database: $DatabaseName" -ForegroundColor Yellow
    Write-Host "Path: $DatabasePath" -ForegroundColor Gray
    
    if (-not (Test-Path $DatabasePath)) {
        Write-Host "Database file not found!" -ForegroundColor Red
        return
    }
    
    $sessionData = @()
    
    if ($sqlite3Path) {
        try {
            # Get table list
            Write-Host "Getting table structure..." -ForegroundColor Cyan
            $tables = & $sqlite3Path $DatabasePath ".tables" 2>$null
            Write-Host "Tables found: $tables" -ForegroundColor Gray
            
            # Query common session-related tables and columns
            $queries = @(
                "SELECT name FROM sqlite_master WHERE type='table';",
                "SELECT * FROM ItemTable WHERE key LIKE '%session%' OR value LIKE '%session%';",
                "SELECT * FROM ItemTable WHERE key LIKE '%chat%' OR value LIKE '%chat%';",
                "SELECT * FROM ItemTable WHERE key LIKE '%augment%' OR value LIKE '%augment%';",
                "SELECT key, SUBSTR(value, 1, 200) as value_preview FROM ItemTable;"
            )
            
            foreach ($query in $queries) {
                try {
                    Write-Host "Executing: $query" -ForegroundColor Cyan
                    $result = & $sqlite3Path $DatabasePath $query 2>$null
                    if ($result) {
                        Write-Host "Results:" -ForegroundColor Green
                        $result | ForEach-Object { Write-Host "  $_" -ForegroundColor White }
                        
                        # Extract UUIDs from results
                        $uuidPattern = '[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}'
                        $matches = [regex]::Matches($result -join " ", $uuidPattern)
                        foreach ($match in $matches) {
                            $sessionData += [PSCustomObject]@{
                                Database = $DatabaseName
                                SessionId = $match.Value
                                Source = "SQL Query Result"
                                Query = $query
                            }
                        }
                    }
                }
                catch {
                    Write-Host "Query failed: $query" -ForegroundColor Red
                }
            }
        }
        catch {
            Write-Host "Error querying database: $_" -ForegroundColor Red
        }
    }
    else {
        # Fallback: Read database as binary and extract UUIDs
        Write-Host "Using binary extraction method..." -ForegroundColor Yellow
        try {
            $bytes = [System.IO.File]::ReadAllBytes($DatabasePath)
            $content = [System.Text.Encoding]::UTF8.GetString($bytes)
            
            $uuidPattern = '[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}'
            $matches = [regex]::Matches($content, $uuidPattern)
            
            $uniqueMatches = $matches | Select-Object Value -Unique
            Write-Host "Found $($uniqueMatches.Count) unique UUIDs in binary content" -ForegroundColor Green
            
            foreach ($match in $uniqueMatches) {
                $sessionData += [PSCustomObject]@{
                    Database = $DatabaseName
                    SessionId = $match.Value
                    Source = "Binary Extraction"
                    Query = "N/A"
                }
            }
        }
        catch {
            Write-Host "Binary extraction failed: $_" -ForegroundColor Red
        }
    }
    
    return $sessionData
}

# Main execution
$allSessionData = @()

# 1. Query main global database
Write-Host "`n[1] Querying main global database..." -ForegroundColor Cyan
$mainDbPath = "globalStorage\state.vscdb"
$mainDbData = Query-SQLiteDatabase -DatabasePath $mainDbPath -DatabaseName "Global State DB"
$allSessionData += $mainDbData

# 2. Query workspace databases
Write-Host "`n[2] Querying workspace databases..." -ForegroundColor Cyan
$workspaces = Get-ChildItem "workspaceStorage" -Directory

foreach ($workspace in $workspaces) {
    Write-Host "`nWorkspace: $($workspace.Name)" -ForegroundColor Yellow
    
    # Main workspace database
    $dbPath = Join-Path $workspace.FullName "state.vscdb"
    if (Test-Path $dbPath) {
        $dbData = Query-SQLiteDatabase -DatabasePath $dbPath -DatabaseName "Workspace-$($workspace.Name)"
        $allSessionData += $dbData
    }
    
    # Backup database
    $backupDbPath = Join-Path $workspace.FullName "state.vscdb.backup"
    if (Test-Path $backupDbPath) {
        $backupData = Query-SQLiteDatabase -DatabasePath $backupDbPath -DatabaseName "Workspace-$($workspace.Name)-Backup"
        $allSessionData += $backupData
    }
}

# 3. Results summary
Write-Host "`n=== DATABASE EXTRACTION RESULTS ===" -ForegroundColor Green
Write-Host "Total SessionIds found in databases: $($allSessionData.Count)" -ForegroundColor Yellow

if ($allSessionData.Count -gt 0) {
    # Group by database
    $groupedData = $allSessionData | Group-Object Database
    foreach ($group in $groupedData) {
        Write-Host "`n$($group.Name): $($group.Count) SessionIds" -ForegroundColor Cyan
        $uniqueIds = $group.Group | Select-Object SessionId -Unique
        foreach ($id in $uniqueIds) {
            Write-Host "  $($id.SessionId)" -ForegroundColor Green
        }
    }
    
    # Export results
    $allSessionData | Export-Csv "database_sessionids.csv" -NoTypeInformation -Encoding UTF8
    $allSessionData | Format-Table -AutoSize | Out-File "database_sessionids_report.txt" -Encoding UTF8
    
    Write-Host "`nDatabase results exported to:" -ForegroundColor Green
    Write-Host "- database_sessionids.csv" -ForegroundColor Cyan
    Write-Host "- database_sessionids_report.txt" -ForegroundColor Cyan
}

Write-Host "`nDatabase extraction completed!" -ForegroundColor Green
