package com.example.surveyassistant.ui.theme

import androidx.compose.ui.graphics.Color

// 勘测助手蓝白主题色彩
// 主要蓝色系
val SurveyBlue = Color(0xFF1976D2)        // 主蓝色
val SurveyBlueLight = Color(0xFF42A5F5)   // 浅蓝色
val SurveyBlueDark = Color(0xFF0D47A1)    // 深蓝色

// 辅助色彩
val SurveyWhite = Color(0xFFFFFFFF)       // 纯白色
val SurveyGray = Color(0xFFF5F5F5)        // 浅灰色
val SurveyGrayMedium = Color(0xFFE0E0E0)  // 中灰色
val SurveyGrayDark = Color(0xFF757575)    // 深灰色

// 功能色彩
val SurveyGreen = Color(0xFF4CAF50)       // 成功/确认色
val SurveyRed = Color(0xFFF44336)         // 错误/警告色
val SurveyOrange = Color(0xFFFF9800)      // 提醒色
val SurveyYellow = Color(0xFFFFC107)      // 高亮色

// 深色主题色彩
val SurveyBlueDarkTheme = Color(0xFF90CAF9)     // 深色主题主蓝色
val SurveyGrayDarkTheme = Color(0xFF121212)     // 深色主题背景
val SurveyGrayMediumDarkTheme = Color(0xFF1E1E1E) // 深色主题卡片背景