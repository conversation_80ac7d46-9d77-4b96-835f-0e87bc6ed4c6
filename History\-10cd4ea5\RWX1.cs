using System;
using System.Globalization;
using System.Windows;
using System.Windows.Data;
using XIAOFUTools.Tools.AI.Models;

namespace XIAOFUTools.Tools.AI.Controls
{
    /// <summary>
    /// 消息类型到可见性转换器
    /// </summary>
    public class MessageTypeToVisibilityConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is MessageType messageType && parameter is string targetTypeString)
            {
                if (Enum.TryParse<MessageType>(targetTypeString, out var expectedMessageType))
                {
                    return messageType == expectedMessageType ? Visibility.Visible : Visibility.Collapsed;
                }
            }

            return Visibility.Collapsed;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
