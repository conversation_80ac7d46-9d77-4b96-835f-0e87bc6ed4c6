package com.example.surveyassistant.ui.theme

import androidx.compose.ui.graphics.Color

// 勘测助手固定蓝白配色方案
// 主要蓝色系
val Primary = Color(0xFF1976D2)           // 主蓝色
val PrimaryLight = Color(0xFF42A5F5)      // 浅蓝色
val PrimaryDark = Color(0xFF0D47A1)       // 深蓝色
val PrimaryVariant = Color(0xFF1565C0)    // 蓝色变体

// 背景和表面色彩
val Background = Color(0xFFFFFFFF)        // 纯白背景
val Surface = Color(0xFFF8F9FA)           // 浅灰表面
val SurfaceVariant = Color(0xFFF0F4F8)    // 表面变体

// 文本色彩
val OnPrimary = Color(0xFFFFFFFF)         // 主色上的文本（白色）
val OnBackground = Color(0xFF1A1A1A)      // 背景上的文本（深色）
val OnSurface = Color(0xFF2C3E50)         // 表面上的文本
val OnSurfaceVariant = Color(0xFF546E7A)  // 表面变体上的文本

// 边框和分割线
val Outline = Color(0xFFE1E8ED)           // 边框色
val OutlineVariant = Color(0xFFCFD8DC)    // 边框变体

// 功能色彩
val Success = Color(0xFF4CAF50)           // 成功色（绿色）
val Error = Color(0xFFF44336)             // 错误色（红色）
val Warning = Color(0xFFFF9800)           // 警告色（橙色）
val Info = Color(0xFF2196F3)             // 信息色（蓝色）

// 透明度变体
val PrimaryAlpha10 = Color(0x1A1976D2)    // 主色10%透明度
val PrimaryAlpha20 = Color(0x331976D2)    // 主色20%透明度
val SurfaceAlpha90 = Color(0xE6F8F9FA)    // 表面色90%透明度