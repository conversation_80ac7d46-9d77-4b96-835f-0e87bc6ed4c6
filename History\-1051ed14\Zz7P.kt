package com.example.babylog.ui.screens

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.navigation.NavController
import com.example.babylog.data.database.BabyLogDatabase
import com.example.babylog.data.entity.Baby
import com.example.babylog.data.entity.FeedingRecord
import com.example.babylog.data.repository.BabyRepository
import com.example.babylog.navigation.Screen
import com.example.babylog.ui.theme.*
import java.text.SimpleDateFormat
import java.util.*

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun FeedingScreen(navController: NavController) {
    val context = LocalContext.current
    val database = BabyLogDatabase.getDatabase(context)
    val repository = BabyRepository(
        database.babyDao(),
        database.healthRecordDao(),
        database.milestoneDao(),
        database.feedingRecordDao(),
        database.photoDao()
    )
    
    val babies by repository.getAllBabies().collectAsState(initial = emptyList())
    var selectedBaby by remember { mutableStateOf<Baby?>(null) }
    var selectedTab by remember { mutableStateOf(0) }
    
    // Set first baby as selected if available
    LaunchedEffect(babies) {
        if (babies.isNotEmpty() && selectedBaby == null) {
            selectedBaby = babies.first()
        }
    }
    
    val feedingRecords by remember(selectedBaby) {
        if (selectedBaby != null) {
            repository.getFeedingRecordsByBaby(selectedBaby!!.id)
        } else {
            kotlinx.coroutines.flow.flowOf(emptyList<FeedingRecord>())
        }
    }.collectAsState(initial = emptyList())
    
    val tabs = listOf("全部", "母乳喂养", "奶瓶喂养", "辅食")
    
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(NeumorphismColors.background)
    ) {
        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = 20.dp),
            contentPadding = PaddingValues(vertical = 16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // Header
            item {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Column {
                        Text(
                            text = "喂养记录",
                            style = MaterialTheme.typography.headlineLarge,
                            fontWeight = FontWeight.Bold,
                            color = NeumorphismColors.textPrimary
                        )
                        Text(
                            text = "管理宝宝的喂养数据",
                            style = MaterialTheme.typography.bodyMedium,
                            color = NeumorphismColors.textSecondary
                        )
                    }

                    selectedBaby?.let { baby ->
                        NeumorphismFloatingActionButton(
                            onClick = {
                                navController.navigate("${Screen.AddFeeding.route}?babyId=${baby.id}")
                            },
                            modifier = Modifier.size(56.dp)
                        ) {
                            Icon(
                                Icons.Default.Add,
                                contentDescription = "添加记录",
                                tint = Color.White
                            )
                        }
                    }
                }
            }

            // Baby selector
            if (babies.isNotEmpty()) {
                item {
                    var expanded by remember { mutableStateOf(false) }
            
            ExposedDropdownMenuBox(
                expanded = expanded,
                onExpandedChange = { expanded = !expanded }
            ) {
                OutlinedTextField(
                    value = selectedBaby?.name ?: "",
                    onValueChange = { },
                    readOnly = true,
                    label = { Text("选择宝宝") },
                    trailingIcon = {
                        ExposedDropdownMenuDefaults.TrailingIcon(expanded = expanded)
                    },
                    modifier = Modifier
                        .fillMaxWidth()
                        .menuAnchor()
                )
                
                ExposedDropdownMenu(
                    expanded = expanded,
                    onDismissRequest = { expanded = false }
                ) {
                    babies.forEach { baby ->
                        DropdownMenuItem(
                            text = { Text(baby.name) },
                            onClick = {
                                selectedBaby = baby
                                expanded = false
                            }
                        )
                    }
                }
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // Quick stats
            selectedBaby?.let { baby ->
                FeedingStatsCard(
                    feedingRecords = feedingRecords,
                    baby = baby
                )
                
                Spacer(modifier = Modifier.height(16.dp))
            }
            
            // Tabs
            ScrollableTabRow(selectedTabIndex = selectedTab) {
                tabs.forEachIndexed { index, title ->
                    Tab(
                        selected = selectedTab == index,
                        onClick = { selectedTab = index },
                        text = { Text(title) }
                    )
                }
            }

            // Feeding records
            selectedBaby?.let { baby ->
                if (feedingRecords.isEmpty()) {
                    item {
                        NeumorphismCard(
                            modifier = Modifier.fillMaxWidth(),
                            elevation = 6,
                            cornerRadius = 20
                        ) {
                            Column(
                                horizontalAlignment = Alignment.CenterHorizontally
                            ) {
                                Icon(
                                    Icons.Default.Restaurant,
                                    contentDescription = null,
                                    modifier = Modifier.size(64.dp),
                                    tint = NeumorphismColors.primary
                                )
                                Spacer(modifier = Modifier.height(16.dp))
                                Text(
                                    text = "还没有喂养记录",
                                    style = MaterialTheme.typography.titleLarge,
                                    fontWeight = FontWeight.Bold,
                                    color = NeumorphismColors.textPrimary
                                )
                                Spacer(modifier = Modifier.height(8.dp))
                                Text(
                                    text = "点击右上角的+号添加第一条喂养记录",
                                    style = MaterialTheme.typography.bodyMedium,
                                    color = NeumorphismColors.textSecondary
                                )
                            }
                        }
                    }
                } else {
                    items(feedingRecords) { record ->
                        FeedingRecordCard(record = record)
                    }
                }
            }
        }
    }
}

@Composable
fun FeedingRecordCard(record: com.example.babylog.data.entity.FeedingRecord) {
    NeumorphismCard(
        modifier = Modifier.fillMaxWidth(),
        elevation = 4,
        cornerRadius = 16
    ) {
        Column {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = when (record.type) {
                        "breastfeeding" -> "母乳喂养"
                        "bottle" -> "奶瓶喂养"
                        "solid_food" -> "辅食喂养"
                        else -> "喂养记录"
                    },
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold,
                    color = NeumorphismColors.textPrimary
                )
                Text(
                    text = SimpleDateFormat("MM-dd HH:mm", Locale.getDefault()).format(record.startTime),
                    style = MaterialTheme.typography.bodySmall,
                    color = NeumorphismColors.textSecondary
                )
            }

            Spacer(modifier = Modifier.height(8.dp))

            when (record.type) {
                "breastfeeding" -> {
                    record.duration?.let { duration ->
                        Text(
                            text = "时长：${duration}分钟",
                            style = MaterialTheme.typography.bodyMedium,
                            color = NeumorphismColors.primary
                        )
                    }
                }
                "bottle" -> {
                    record.amount?.let { amount ->
                        Text(
                            text = "奶量：${amount}ml",
                            style = MaterialTheme.typography.bodyMedium,
                            color = NeumorphismColors.primary
                        )
                    }
                }
                "solid_food" -> {
                    record.foodName?.let { foodName ->
                        Text(
                            text = "食物：$foodName",
                            style = MaterialTheme.typography.bodyMedium,
                            color = NeumorphismColors.primary
                        )
                    }
                    record.amount?.let { amount ->
                        Text(
                            text = "食用量：${amount}g",
                            style = MaterialTheme.typography.bodySmall,
                            color = NeumorphismColors.textSecondary
                        )
                    }
                }
            }

            record.notes?.let { notes ->
                if (notes.isNotBlank()) {
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = notes,
                        style = MaterialTheme.typography.bodySmall,
                        color = NeumorphismColors.textSecondary
                    )
                }
            }
        }
    }
}
