using System;
using System.Collections.Generic;
using System.Linq;

namespace shptxttokml.Models
{
    /// <summary>
    /// 坐标系信息
    /// </summary>
    public class CoordinateSystemInfo
    {
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public int EpsgCode { get; set; }
        public string Category { get; set; } = string.Empty;

        public override string ToString()
        {
            return $"{Name} - {Description}";
        }
    }

    /// <summary>
    /// 坐标系管理器
    /// </summary>
    public static class CoordinateSystemManager
    {
        /// <summary>
        /// 获取所有支持的坐标系
        /// </summary>
        public static List<CoordinateSystemInfo> GetAllCoordinateSystems()
        {
            var systems = new List<CoordinateSystemInfo>();

            // CGCS2000 3度分带坐标系 (25-45带)
            for (int zone = 25; zone <= 45; zone++)
            {
                systems.Add(new CoordinateSystemInfo
                {
                    Name = $"CGCS2000_3_Degree_GK_Zone_{zone}",
                    Description = $"CGCS2000 3度分带第{zone}带",
                    EpsgCode = 2333 + (zone - 25), // 25带对应EPSG:2333
                    Category = "CGCS2000 3度分带"
                });
            }

            // CGCS2000 6度分带坐标系 (13-23带)
            for (int zone = 13; zone <= 23; zone++)
            {
                systems.Add(new CoordinateSystemInfo
                {
                    Name = $"CGCS2000_6_Degree_GK_Zone_{zone}",
                    Description = $"CGCS2000 6度分带第{zone}带",
                    EpsgCode = 4491 + (zone - 13), // 13带对应EPSG:4491
                    Category = "CGCS2000 6度分带"
                });
            }

            // 常用的其他坐标系
            systems.AddRange(new[]
            {
                new CoordinateSystemInfo
                {
                    Name = "CGCS2000_Geographic",
                    Description = "CGCS2000 地理坐标系",
                    EpsgCode = 4490,
                    Category = "地理坐标系"
                },
                new CoordinateSystemInfo
                {
                    Name = "WGS84_Geographic",
                    Description = "WGS84 地理坐标系",
                    EpsgCode = 4326,
                    Category = "地理坐标系"
                },
                new CoordinateSystemInfo
                {
                    Name = "Beijing1954_Geographic",
                    Description = "北京1954 地理坐标系",
                    EpsgCode = 4214,
                    Category = "地理坐标系"
                },
                new CoordinateSystemInfo
                {
                    Name = "Xian1980_Geographic",
                    Description = "西安1980 地理坐标系",
                    EpsgCode = 4610,
                    Category = "地理坐标系"
                }
            });

            // Beijing 1954 6度分带坐标系 (13-23带)
            for (int zone = 13; zone <= 23; zone++)
            {
                systems.Add(new CoordinateSystemInfo
                {
                    Name = $"Beijing1954_6_Degree_GK_Zone_{zone}",
                    Description = $"北京1954 6度分带第{zone}带",
                    EpsgCode = 2422 + (zone - 13), // 13带对应EPSG:2422
                    Category = "北京1954 6度分带"
                });
            }

            // Xian 1980 6度分带坐标系 (13-23带)
            for (int zone = 13; zone <= 23; zone++)
            {
                systems.Add(new CoordinateSystemInfo
                {
                    Name = $"Xian1980_6_Degree_GK_Zone_{zone}",
                    Description = $"西安1980 6度分带第{zone}带",
                    EpsgCode = 2349 + (zone - 13), // 13带对应EPSG:2349
                    Category = "西安1980 6度分带"
                });
            }

            return systems.OrderBy(s => s.Category).ThenBy(s => s.EpsgCode).ToList();
        }

        /// <summary>
        /// 获取默认坐标系
        /// </summary>
        public static CoordinateSystemInfo GetDefaultCoordinateSystem()
        {
            return new CoordinateSystemInfo
            {
                Name = "CGCS2000_3_Degree_GK_Zone_37",
                Description = "CGCS2000 3度分带第37带",
                EpsgCode = 2345, // 37带对应EPSG:2345
                Category = "CGCS2000 3度分带"
            };
        }

        /// <summary>
        /// 根据EPSG代码查找坐标系
        /// </summary>
        public static CoordinateSystemInfo? FindByEpsgCode(int epsgCode)
        {
            return GetAllCoordinateSystems().FirstOrDefault(s => s.EpsgCode == epsgCode);
        }

        /// <summary>
        /// 根据名称查找坐标系
        /// </summary>
        public static CoordinateSystemInfo? FindByName(string name)
        {
            return GetAllCoordinateSystems().FirstOrDefault(s => s.Name.Equals(name, StringComparison.OrdinalIgnoreCase));
        }
    }
}
