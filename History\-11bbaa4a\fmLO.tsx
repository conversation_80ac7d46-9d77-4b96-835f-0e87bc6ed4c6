import React, { useState, useCallback } from 'react';
import { Form, FormProps, FormInstance } from 'antd';
import { ValidationSchema, validateObject, ValidationResult } from '../../utils/validation';

interface ValidatedFormProps extends Omit<FormProps, 'onFinish'> {
  validationSchema?: ValidationSchema;
  onFinish?: (values: any, validationResult: ValidationResult) => void;
  onValidationError?: (errors: string[]) => void;
  validateOnChange?: boolean;
  validateOnBlur?: boolean;
  showErrorMessages?: boolean;
  children?: React.ReactNode;
}

interface ValidatedFormRef {
  form: FormInstance;
  validate: () => Promise<ValidationResult>;
  validateField: (field: string) => Promise<ValidationResult>;
}

const ValidatedForm = React.forwardRef<ValidatedFormRef, ValidatedFormProps>(({
  validationSchema,
  onFinish,
  onValidationError,
  validateOnChange = false,
  validateOnBlur = true,
  showErrorMessages = true,
  children,
  ...formProps
}, ref) => {
  const [form] = Form.useForm();
  const [validationErrors, setValidationErrors] = useState<Record<string, string[]>>({});

  // 验证单个字段
  const validateField = useCallback(async (field: string): Promise<ValidationResult> => {
    if (!validationSchema || !validationSchema[field]) {
      return { valid: true, errors: [] };
    }

    try {
      const value = form.getFieldValue(field);
      const rules = validationSchema[field];
      
      // 使用自定义验证逻辑
      const result = await import('../../utils/validation').then(module => 
        module.validateValue(value, rules)
      );

      // 更新字段错误状态
      setValidationErrors(prev => ({
        ...prev,
        [field]: result.errors
      }));

      // 设置 Ant Design 表单错误
      if (result.errors.length > 0) {
        form.setFields([{
          name: field,
          errors: result.errors
        }]);
      } else {
        form.setFields([{
          name: field,
          errors: []
        }]);
      }

      return result;
    } catch (error) {
      console.error('Field validation error:', error);
      return { valid: false, errors: ['验证过程中发生错误'] };
    }
  }, [form, validationSchema]);

  // 验证整个表单
  const validateForm = useCallback(async (): Promise<ValidationResult> => {
    if (!validationSchema) {
      return { valid: true, errors: [] };
    }

    try {
      const values = form.getFieldsValue();
      const result = validateObject(values, validationSchema);

      // 更新所有字段的错误状态
      const fieldErrors: Record<string, string[]> = {};
      const formFields: Array<{ name: string; errors: string[] }> = [];

      Object.keys(validationSchema).forEach(field => {
        const fieldResult = import('../../utils/validation').then(module => 
          module.validateValue(values[field], validationSchema[field])
        );
        
        fieldResult.then(res => {
          fieldErrors[field] = res.errors;
          formFields.push({
            name: field,
            errors: res.errors
          });
        });
      });

      setValidationErrors(fieldErrors);
      form.setFields(formFields);

      if (!result.valid && onValidationError) {
        onValidationError(result.errors);
      }

      return result;
    } catch (error) {
      console.error('Form validation error:', error);
      return { valid: false, errors: ['表单验证过程中发生错误'] };
    }
  }, [form, validationSchema, onValidationError]);

  // 处理表单提交
  const handleFinish = useCallback(async (values: any) => {
    const validationResult = await validateForm();
    
    if (onFinish) {
      onFinish(values, validationResult);
    }
  }, [validateForm, onFinish]);

  // 处理字段值变化
  const handleValuesChange = useCallback((changedValues: any, allValues: any) => {
    if (validateOnChange && validationSchema) {
      Object.keys(changedValues).forEach(field => {
        if (validationSchema[field]) {
          validateField(field);
        }
      });
    }

    if (formProps.onValuesChange) {
      formProps.onValuesChange(changedValues, allValues);
    }
  }, [validateOnChange, validationSchema, validateField, formProps]);

  // 暴露方法给父组件
  React.useImperativeHandle(ref, () => ({
    form,
    validate: validateForm,
    validateField
  }), [form, validateForm, validateField]);

  return (
    <Form
      {...formProps}
      form={form}
      onFinish={handleFinish}
      onValuesChange={handleValuesChange}
      validateTrigger={validateOnBlur ? 'onBlur' : 'onChange'}
    >
      {children}
    </Form>
  );
});

ValidatedForm.displayName = 'ValidatedForm';

export default ValidatedForm;
export type { ValidatedFormRef };
